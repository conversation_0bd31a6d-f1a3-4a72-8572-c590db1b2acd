{"version": 3, "file": "react-katex.modern.js", "sources": ["../src/index.tsx"], "sourcesContent": ["import React, {\n  ComponentPropsWithoutRef,\n  useState,\n  useEffect,\n  ReactElement,\n  ElementType,\n  memo,\n} from 'react';\nimport KaTeX, { ParseError, KatexOptions } from 'katex';\n\nconst TeX: React.FC<TeXProps> = ({\n  children,\n  math,\n  block,\n  errorColor,\n  renderError,\n  settings,\n  as: asComponent,\n  ...props\n}) => {\n  const Component = asComponent || (block ? 'div' : 'span');\n  const content = (children ?? math) as string;\n  const [state, setState] = useState<\n    { innerHtml: string } | { errorElement: React.ReactElement }\n  >({ innerHtml: '' });\n\n  useEffect(() => {\n    try {\n      const innerHtml = KaTeX.renderToString(content, {\n        displayMode: !!block,\n        errorColor,\n        throwOnError: !!renderError,\n        ...settings,\n      });\n\n      setState({ innerHtml });\n    } catch (error) {\n      if (error instanceof KaTeX.ParseError || error instanceof TypeError) {\n        if (renderError) {\n          setState({ errorElement: renderError(error) });\n        } else {\n          setState({ innerHtml: error.message });\n        }\n      } else {\n        throw error;\n      }\n    }\n  }, [block, content, errorColor, renderError, settings]);\n\n  if ('errorElement' in state) {\n    return state.errorElement;\n  }\n\n  return (\n    <Component\n      {...props}\n      dangerouslySetInnerHTML={{ __html: state.innerHtml }}\n    />\n  );\n};\n\nexport default memo(TeX);\n\ntype TeXProps = ComponentPropsWithoutRef<'div'> &\n  Partial<{\n    as: ElementType;\n    math: string | number;\n    block: boolean;\n    errorColor: string;\n    renderError: (error: ParseError | TypeError) => ReactElement;\n    settings: KatexOptions;\n  }>;\n"], "names": ["memo", "children", "math", "block", "errorColor", "renderError", "settings", "as", "asComponent", "props", "Component", "content", "state", "setState", "useState", "innerHtml", "useEffect", "KaTeX", "renderToString", "displayMode", "throwOnError", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TypeError", "errorElement", "message", "React", "dangerouslySetInnerHTML", "__html"], "mappings": "kFA6DA,MAAeA,EAnDiB,EAC9BC,SAAAA,EACAC,KAAAA,EACAC,MAAAA,EACAC,WAAAA,EACAC,YAAAA,EACAC,SAAAA,EACAC,GAAIC,KACDC,MAEH,MAAMC,EAAYF,IAAgBL,EAAQ,MAAQ,QAC5CQ,EAAWV,GAAYC,GACtBU,EAAOC,GAAYC,EAExB,CAAEC,UAAW,KAyBf,OAvBAC,EAAU,KACR,IACE,MAAMD,EAAYE,EAAMC,eAAeP,EAAS,CAC9CQ,cAAehB,EACfC,WAAAA,EACAgB,eAAgBf,KACbC,IAGLO,EAAS,CAAEE,UAAAA,IACX,MAAOM,GACP,KAAIA,aAAiBJ,EAAMK,YAAcD,aAAiBE,WAOxD,MAAMF,EALJR,EADER,EACO,CAAEmB,aAAcnB,EAAYgB,IAE5B,CAAEN,UAAWM,EAAMI,YAMjC,CAACtB,EAAOQ,EAASP,EAAYC,EAAaC,IAEzC,iBAAkBM,EACbA,EAAMY,aAIbE,gBAAChB,mBACKD,GACJkB,wBAAyB,CAAEC,OAAQhB,EAAMG"}