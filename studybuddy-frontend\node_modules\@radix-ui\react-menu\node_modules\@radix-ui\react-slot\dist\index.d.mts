import * as react_jsx_runtime from 'react/jsx-runtime';
import * as React from 'react';

interface SlotProps extends React.HTMLAttributes<HTMLElement> {
    children?: React.ReactNode;
}
declare const Slot: React.ForwardRefExoticComponent<SlotProps & React.RefAttributes<HTMLElement>>;
declare const Slottable: ({ children }: {
    children: React.ReactNode;
}) => react_jsx_runtime.JSX.Element;
declare const Root: React.ForwardRefExoticComponent<SlotProps & React.RefAttributes<HTMLElement>>;

export { Root, Slot, type SlotProps, Slottable };
