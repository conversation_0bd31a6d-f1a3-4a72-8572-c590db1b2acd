import { Flame } from "lucide-react"

interface ProfileCardProps {
  user: {
    name: string
    score: number
    avatar: string
    position: number
  }
}

export function ProfileCard({ user }: ProfileCardProps) {
  return (
    <div className="bg-white rounded-xl border border-gray-200 p-6 text-center">
      <img
        src={user.avatar || "/placeholder.svg?height=80&width=80"}
        alt={user.name}
        className="w-20 h-20 rounded-full mx-auto mb-4 border-4 border-blue-100"
      />

      <h3 className="font-semibold text-lg text-gray-900 mb-2">{user.name}</h3>

      <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium inline-flex items-center gap-1 mb-3">
        <Flame className="h-3 w-3" />
        {user.score}
      </div>

      <div className="bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium">
        #{user.position} Position
      </div>

      <p className="text-gray-500 text-sm mt-4">
        Congrats Naagin,
        <br />
        Keep Crushing!
      </p>
    </div>
  )
}
