import { Search, Flame } from "lucide-react"

interface LeaderboardUser {
  id: string
  name: string
  score: number
  avatar: string
  studentId: string
}

interface LeaderboardListProps {
  users: LeaderboardUser[]
}

export function LeaderboardList({ users }: LeaderboardListProps) {
  const sortedUsers = [...users].sort((a, b) => b.score - a.score)

  const getPositionColor = (position: number) => {
    switch (position) {
      case 1:
        return "bg-yellow-500"
      case 2:
        return "bg-blue-500"
      case 3:
        return "bg-orange-500"
      default:
        return "bg-gray-400"
    }
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 p-4 md:p-6">
      {/* Search Bar */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <input
          type="text"
          placeholder="Search for a friend..."
          className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Leaderboard List */}
      <div className="space-y-3">
        {sortedUsers.map((user, index) => {
          const position = index + 1
          return (
            <div key={user.id} className="flex items-center gap-4 p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div
                className={`w-8 h-8 ${getPositionColor(position)} rounded-full flex items-center justify-center text-white font-bold text-sm`}
              >
                {position}
              </div>

              <img
                src={user.avatar || "/placeholder.svg?height=40&width=40"}
                alt={user.name}
                className="w-10 h-10 rounded-full"
              />

              <div className="flex-1">
                <div className="font-medium text-gray-900">{user.name}</div>
                <div className="text-sm text-gray-500">{user.studentId}</div>
              </div>

              <div className="flex items-center gap-1 text-orange-500 font-medium">
                {user.score}
                <Flame className="h-4 w-4" />
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
