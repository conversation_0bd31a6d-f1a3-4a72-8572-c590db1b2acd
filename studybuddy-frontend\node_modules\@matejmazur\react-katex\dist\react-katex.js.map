{"version": 3, "file": "react-katex.js", "sources": ["../src/index.tsx"], "sourcesContent": ["import React, {\n  ComponentPropsWithoutRef,\n  useState,\n  useEffect,\n  ReactElement,\n  ElementType,\n  memo,\n} from 'react';\nimport KaTeX, { ParseError, KatexOptions } from 'katex';\n\nconst TeX: React.FC<TeXProps> = ({\n  children,\n  math,\n  block,\n  errorColor,\n  renderError,\n  settings,\n  as: asComponent,\n  ...props\n}) => {\n  const Component = asComponent || (block ? 'div' : 'span');\n  const content = (children ?? math) as string;\n  const [state, setState] = useState<\n    { innerHtml: string } | { errorElement: React.ReactElement }\n  >({ innerHtml: '' });\n\n  useEffect(() => {\n    try {\n      const innerHtml = KaTeX.renderToString(content, {\n        displayMode: !!block,\n        errorColor,\n        throwOnError: !!renderError,\n        ...settings,\n      });\n\n      setState({ innerHtml });\n    } catch (error) {\n      if (error instanceof KaTeX.ParseError || error instanceof TypeError) {\n        if (renderError) {\n          setState({ errorElement: renderError(error) });\n        } else {\n          setState({ innerHtml: error.message });\n        }\n      } else {\n        throw error;\n      }\n    }\n  }, [block, content, errorColor, renderError, settings]);\n\n  if ('errorElement' in state) {\n    return state.errorElement;\n  }\n\n  return (\n    <Component\n      {...props}\n      dangerouslySetInnerHTML={{ __html: state.innerHtml }}\n    />\n  );\n};\n\nexport default memo(TeX);\n\ntype TeXProps = ComponentPropsWithoutRef<'div'> &\n  Partial<{\n    as: ElementType;\n    math: string | number;\n    block: boolean;\n    errorColor: string;\n    renderError: (error: ParseError | TypeError) => ReactElement;\n    settings: KatexOptions;\n  }>;\n"], "names": ["memo", "children", "math", "block", "errorColor", "renderError", "settings", "asComponent", "as", "props", "Component", "content", "useState", "innerHtml", "state", "setState", "useEffect", "KaTeX", "renderToString", "displayMode", "throwOnError", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TypeError", "errorElement", "message", "React", "dangerouslySetInnerHTML", "__html"], "mappings": "yUAUA,MAmDeA,OAnDiB,gBAC9BC,IAAAA,SACAC,IAAAA,KACAC,IAAAA,MACAC,IAAAA,WACAC,IAAAA,YACAC,IAAAA,SACIC,IAAJC,GACGC,+MAEGC,EAAYH,IAAgBJ,EAAQ,MAAQ,QAC5CQ,QAAWV,EAAAA,EAAYC,IACHU,WAExB,CAAEC,UAAW,KAFRC,OAAOC,OA2Bd,OAvBAC,YAAU,WACR,IACE,IAAMH,EAAYI,EAAMC,eAAeP,KACrCQ,cAAehB,EACfC,WAAAA,EACAgB,eAAgBf,GACbC,IAGLS,EAAS,CAAEF,UAAAA,IACX,MAAOQ,GACP,KAAIA,aAAiBJ,EAAMK,YAAcD,aAAiBE,WAOxD,MAAMF,EALJN,EADEV,EACO,CAAEmB,aAAcnB,EAAYgB,IAE5B,CAAER,UAAWQ,EAAMI,YAMjC,CAACtB,EAAOQ,EAASP,EAAYC,EAAaC,IAEzC,iBAAkBQ,EACbA,EAAMU,aAIbE,gBAAChB,mBACKD,GACJkB,wBAAyB,CAAEC,OAAQd,EAAMD"}