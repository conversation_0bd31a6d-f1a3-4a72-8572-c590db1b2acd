/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5CVelocity%5Cstudybuddy-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5CVelocity%5Cstudybuddy-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5CVelocity%5Cstudybuddy-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5CVelocity%5Cstudybuddy-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZkYXNoYm9hcmQlMkZwYWdlJnBhZ2U9JTJGZGFzaGJvYXJkJTJGcGFnZSZhcHBQYXRocz0lMkZkYXNoYm9hcmQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGZGFzaGJvYXJkJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q2FkYXJzJTVDRGVza3RvcCU1Q0ZMJTVDVmVsb2NpdHklNUNzdHVkeWJ1ZGR5LWZyb250ZW5kJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNhZGFycyU1Q0Rlc2t0b3AlNUNGTCU1Q1ZlbG9jaXR5JTVDc3R1ZHlidWRkeS1mcm9udGVuZCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD1zdGFuZGFsb25lJnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNCQUFzQixvSkFBdUg7QUFDN0ksc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQixnT0FBbUY7QUFDekcsb0JBQW9CLG9LQUFnSTtBQUdsSjtBQUNzRDtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHNmQUErUTtBQUNuVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0Msc2ZBQStRO0FBQ25UO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qix1R0FBa0I7QUFDakQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbW9kdWxlMCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcYWRhcnNcXFxcRGVza3RvcFxcXFxGTFxcXFxWZWxvY2l0eVxcXFxzdHVkeWJ1ZGR5LWZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbmNvbnN0IG1vZHVsZTEgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMyA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiKTtcbmNvbnN0IHBhZ2U0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxhZGFyc1xcXFxEZXNrdG9wXFxcXEZMXFxcXFZlbG9jaXR5XFxcXHN0dWR5YnVkZHktZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNzcidcbn07XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnZGFzaGJvYXJkJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U0LCBcIkM6XFxcXFVzZXJzXFxcXGFkYXJzXFxcXERlc2t0b3BcXFxcRkxcXFxcVmVsb2NpdHlcXFxcc3R1ZHlidWRkeS1mcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFDOlxcXFxVc2Vyc1xcXFxhZGFyc1xcXFxEZXNrdG9wXFxcXEZMXFxcXFZlbG9jaXR5XFxcXHN0dWR5YnVkZHktZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFttb2R1bGUwLCBcIkM6XFxcXFVzZXJzXFxcXGFkYXJzXFxcXERlc2t0b3BcXFxcRkxcXFxcVmVsb2NpdHlcXFxcc3R1ZHlidWRkeS1mcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogW21vZHVsZTEsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbidmb3JiaWRkZW4nOiBbbW9kdWxlMiwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCJdLFxuJ3VuYXV0aG9yaXplZCc6IFttb2R1bGUzLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFDOlxcXFxVc2Vyc1xcXFxhZGFyc1xcXFxEZXNrdG9wXFxcXEZMXFxcXFZlbG9jaXR5XFxcXHN0dWR5YnVkZHktZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkM6XFxcXFVzZXJzXFxcXGFkYXJzXFxcXERlc2t0b3BcXFxcRkxcXFxcVmVsb2NpdHlcXFxcc3R1ZHlidWRkeS1mcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL2Rhc2hib2FyZC9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9kYXNoYm9hcmRcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5CVelocity%5Cstudybuddy-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5CVelocity%5Cstudybuddy-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Manrope%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22manrope%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Manrope%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22manrope%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Manrope%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22manrope%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Manrope%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22manrope%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-header.tsx%22%2C%22ids%22%3A%5B%22DashboardHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarProvider%22%2C%22SidebarInset%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-header.tsx%22%2C%22ids%22%3A%5B%22DashboardHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarProvider%22%2C%22SidebarInset%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/dashboard-header.tsx */ \"(rsc)/./src/components/dashboard/dashboard-header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/sidebar.tsx */ \"(rsc)/./src/components/ui/sidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2FkYXJzJTVDJTVDRGVza3RvcCU1QyU1Q0ZMJTVDJTVDVmVsb2NpdHklNUMlNUNzdHVkeWJ1ZGR5LWZyb250ZW5kJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2Rhc2hib2FyZC1oZWFkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyRGFzaGJvYXJkSGVhZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2FkYXJzJTVDJTVDRGVza3RvcCU1QyU1Q0ZMJTVDJTVDVmVsb2NpdHklNUMlNUNzdHVkeWJ1ZGR5LWZyb250ZW5kJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDc2lkZWJhci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTJDJTIyU2lkZWJhclByb3ZpZGVyJTIyJTJDJTIyU2lkZWJhckluc2V0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTUFBd0w7QUFDeEw7QUFDQSwwS0FBbUkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkRhc2hib2FyZEhlYWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGFkYXJzXFxcXERlc2t0b3BcXFxcRkxcXFxcVmVsb2NpdHlcXFxcc3R1ZHlidWRkeS1mcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxkYXNoYm9hcmRcXFxcZGFzaGJvYXJkLWhlYWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGFkYXJzXFxcXERlc2t0b3BcXFxcRkxcXFxcVmVsb2NpdHlcXFxcc3R1ZHlidWRkeS1mcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFx1aVxcXFxzaWRlYmFyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-header.tsx%22%2C%22ids%22%3A%5B%22DashboardHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarProvider%22%2C%22SidebarInset%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-header.tsx%22%2C%22ids%22%3A%5B%22DashboardHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarProvider%22%2C%22SidebarInset%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-header.tsx%22%2C%22ids%22%3A%5B%22DashboardHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarProvider%22%2C%22SidebarInset%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/dashboard-header.tsx */ \"(ssr)/./src/components/dashboard/dashboard-header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/sidebar.tsx */ \"(ssr)/./src/components/ui/sidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2FkYXJzJTVDJTVDRGVza3RvcCU1QyU1Q0ZMJTVDJTVDVmVsb2NpdHklNUMlNUNzdHVkeWJ1ZGR5LWZyb250ZW5kJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2Rhc2hib2FyZC1oZWFkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyRGFzaGJvYXJkSGVhZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2FkYXJzJTVDJTVDRGVza3RvcCU1QyU1Q0ZMJTVDJTVDVmVsb2NpdHklNUMlNUNzdHVkeWJ1ZGR5LWZyb250ZW5kJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDc2lkZWJhci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTJDJTIyU2lkZWJhclByb3ZpZGVyJTIyJTJDJTIyU2lkZWJhckluc2V0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTUFBd0w7QUFDeEw7QUFDQSwwS0FBbUkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkRhc2hib2FyZEhlYWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGFkYXJzXFxcXERlc2t0b3BcXFxcRkxcXFxcVmVsb2NpdHlcXFxcc3R1ZHlidWRkeS1mcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxkYXNoYm9hcmRcXFxcZGFzaGJvYXJkLWhlYWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGFkYXJzXFxcXERlc2t0b3BcXFxcRkxcXFxcVmVsb2NpdHlcXFxcc3R1ZHlidWRkeS1mcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFx1aVxcXFxzaWRlYmFyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-header.tsx%22%2C%22ids%22%3A%5B%22DashboardHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5CVelocity%5C%5Cstudybuddy-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarProvider%22%2C%22SidebarInset%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/dashboard-header.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/dashboard-header.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardHeader: () => (/* binding */ DashboardHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Menu_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(ssr)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _raise_issue_modal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./raise-issue-modal */ \"(ssr)/./src/components/dashboard/raise-issue-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardHeader auto */ \n\n\n\n\n\nfunction DashboardHeader() {\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"flex items-center justify-between p-4 md:p-6 border-b border-gray-200 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarTrigger, {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-bold text-gray-800\",\n                                        children: \"study\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-bold text-blue-500\",\n                                        children: \"buddy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-lg md:text-xl font-medium text-gray-800 hidden md:block\",\n                                children: \"Welcome back, Arjun \\uD83D\\uDC4B\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>setIsModalOpen(true),\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white text-sm px-4 py-2 rounded-full\",\n                                children: \"Raise issue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_raise_issue_modal__WEBPACK_IMPORTED_MODULE_4__.RaiseIssueModal, {\n                isOpen: isModalOpen,\n                onClose: ()=>setIsModalOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/dashboard-header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/raise-issue-modal.tsx":
/*!********************************************************!*\
  !*** ./src/components/dashboard/raise-issue-modal.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RaiseIssueModal: () => (/* binding */ RaiseIssueModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ RaiseIssueModal auto */ \n\n\n\nfunction RaiseIssueModal({ isOpen, onClose }) {\n    const [issue, setIssue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!issue.trim()) return;\n        setIsSubmitting(true);\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Reset form and close modal\n        setIssue(\"\");\n        setIsSubmitting(false);\n        onClose();\n        // You could show a success message here\n        alert(\"Issue submitted successfully!\");\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-md mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Report an Issue\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\raise-issue-modal.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\raise-issue-modal.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\raise-issue-modal.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\raise-issue-modal.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-4 space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: issue,\n                                onChange: (e)=>setIssue(e.target.value),\n                                placeholder: \"Report your Issues...\",\n                                className: \"w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\raise-issue-modal.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\raise-issue-modal.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"submit\",\n                            disabled: isSubmitting || !issue.trim(),\n                            className: \"w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isSubmitting ? \"Submitting...\" : \"Submit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\raise-issue-modal.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\raise-issue-modal.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\raise-issue-modal.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\raise-issue-modal.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/raise-issue-modal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90 rounded-[14px]\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxrWUFDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXFZlbG9jaXR5XFxzdHVkeWJ1ZGR5LWZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxpbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJpbnB1dFwiPj4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1iYXNlIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/separator.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/separator.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ \"(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Separator auto */ \n\n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"horizontal\", decorative = true, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        decorative: decorative,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\separator.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined));\nSeparator.displayName = _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/separator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/sheet.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/sheet.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sheet: () => (/* binding */ Sheet),\n/* harmony export */   SheetClose: () => (/* binding */ SheetClose),\n/* harmony export */   SheetContent: () => (/* binding */ SheetContent),\n/* harmony export */   SheetDescription: () => (/* binding */ SheetDescription),\n/* harmony export */   SheetFooter: () => (/* binding */ SheetFooter),\n/* harmony export */   SheetHeader: () => (/* binding */ SheetHeader),\n/* harmony export */   SheetOverlay: () => (/* binding */ SheetOverlay),\n/* harmony export */   SheetPortal: () => (/* binding */ SheetPortal),\n/* harmony export */   SheetTitle: () => (/* binding */ SheetTitle),\n/* harmony export */   SheetTrigger: () => (/* binding */ SheetTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Sheet,SheetPortal,SheetOverlay,SheetTrigger,SheetClose,SheetContent,SheetHeader,SheetFooter,SheetTitle,SheetDescription auto */ \n\n\n\n\n\nconst Sheet = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Root;\nconst SheetTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Trigger;\nconst SheetClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Close;\nconst SheetPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Portal;\nconst SheetOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Overlay, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props,\n        ref: ref\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 22,\n        columnNumber: 3\n    }, undefined));\nSheetOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Overlay.displayName;\nconst sheetVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\", {\n    variants: {\n        side: {\n            top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n            bottom: \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n            left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n            right: \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\"\n        }\n    },\n    defaultVariants: {\n        side: \"right\"\n    }\n});\nconst SheetContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ side = \"right\", className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SheetPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SheetOverlay, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n                lineNumber: 61,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(sheetVariants({\n                    side\n                }), className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n                lineNumber: 62,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nSheetContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Content.displayName;\nconst SheetHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col space-y-2 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined);\nSheetHeader.displayName = \"SheetHeader\";\nconst SheetFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined);\nSheetFooter.displayName = \"SheetFooter\";\nconst SheetTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-lg font-semibold text-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 109,\n        columnNumber: 3\n    }, undefined));\nSheetTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst SheetDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 121,\n        columnNumber: 3\n    }, undefined));\nSheetDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/sheet.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/sidebar.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/sidebar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar),\n/* harmony export */   SidebarContent: () => (/* binding */ SidebarContent),\n/* harmony export */   SidebarFooter: () => (/* binding */ SidebarFooter),\n/* harmony export */   SidebarGroup: () => (/* binding */ SidebarGroup),\n/* harmony export */   SidebarGroupAction: () => (/* binding */ SidebarGroupAction),\n/* harmony export */   SidebarGroupContent: () => (/* binding */ SidebarGroupContent),\n/* harmony export */   SidebarGroupLabel: () => (/* binding */ SidebarGroupLabel),\n/* harmony export */   SidebarHeader: () => (/* binding */ SidebarHeader),\n/* harmony export */   SidebarInput: () => (/* binding */ SidebarInput),\n/* harmony export */   SidebarInset: () => (/* binding */ SidebarInset),\n/* harmony export */   SidebarMenu: () => (/* binding */ SidebarMenu),\n/* harmony export */   SidebarMenuAction: () => (/* binding */ SidebarMenuAction),\n/* harmony export */   SidebarMenuBadge: () => (/* binding */ SidebarMenuBadge),\n/* harmony export */   SidebarMenuButton: () => (/* binding */ SidebarMenuButton),\n/* harmony export */   SidebarMenuItem: () => (/* binding */ SidebarMenuItem),\n/* harmony export */   SidebarMenuSkeleton: () => (/* binding */ SidebarMenuSkeleton),\n/* harmony export */   SidebarMenuSub: () => (/* binding */ SidebarMenuSub),\n/* harmony export */   SidebarMenuSubButton: () => (/* binding */ SidebarMenuSubButton),\n/* harmony export */   SidebarMenuSubItem: () => (/* binding */ SidebarMenuSubItem),\n/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),\n/* harmony export */   SidebarRail: () => (/* binding */ SidebarRail),\n/* harmony export */   SidebarSeparator: () => (/* binding */ SidebarSeparator),\n/* harmony export */   SidebarTrigger: () => (/* binding */ SidebarTrigger),\n/* harmony export */   useSidebar: () => (/* binding */ useSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_PanelLeft_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=PanelLeft!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/panel-left.js\");\n/* harmony import */ var _hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-mobile */ \"(ssr)/./src/hooks/use-mobile.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(ssr)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/sheet */ \"(ssr)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(ssr)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(ssr)/./src/components/ui/tooltip.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar,SidebarContent,SidebarFooter,SidebarGroup,SidebarGroupAction,SidebarGroupContent,SidebarGroupLabel,SidebarHeader,SidebarInput,SidebarInset,SidebarMenu,SidebarMenuAction,SidebarMenuBadge,SidebarMenuButton,SidebarMenuItem,SidebarMenuSkeleton,SidebarMenuSub,SidebarMenuSubButton,SidebarMenuSubItem,SidebarProvider,SidebarRail,SidebarSeparator,SidebarTrigger,useSidebar auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\nconst SIDEBAR_WIDTH = \"16rem\";\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\nconst SidebarContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);\nfunction useSidebar() {\n    const context = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SidebarContext);\n    if (!context) {\n        throw new Error(\"useSidebar must be used within a SidebarProvider.\");\n    }\n    return context;\n}\nconst SidebarProvider = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ defaultOpen = true, open: openProp, onOpenChange: setOpenProp, className, style, children, ...props }, ref)=>{\n    const isMobile = (0,_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile)();\n    const [openMobile, setOpenMobile] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(defaultOpen);\n    const open = openProp ?? _open;\n    const setOpen = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"SidebarProvider.useCallback[setOpen]\": (value)=>{\n            const openState = typeof value === \"function\" ? value(open) : value;\n            if (setOpenProp) {\n                setOpenProp(openState);\n            } else {\n                _setOpen(openState);\n            }\n            // This sets the cookie to keep the sidebar state.\n            document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\n        }\n    }[\"SidebarProvider.useCallback[setOpen]\"], [\n        setOpenProp,\n        open\n    ]);\n    // Helper to toggle the sidebar.\n    const toggleSidebar = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"SidebarProvider.useCallback[toggleSidebar]\": ()=>{\n            return isMobile ? setOpenMobile({\n                \"SidebarProvider.useCallback[toggleSidebar]\": (open)=>!open\n            }[\"SidebarProvider.useCallback[toggleSidebar]\"]) : setOpen({\n                \"SidebarProvider.useCallback[toggleSidebar]\": (open)=>!open\n            }[\"SidebarProvider.useCallback[toggleSidebar]\"]);\n        }\n    }[\"SidebarProvider.useCallback[toggleSidebar]\"], [\n        isMobile,\n        setOpen,\n        setOpenMobile\n    ]);\n    // Adds a keyboard shortcut to toggle the sidebar.\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SidebarProvider.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"SidebarProvider.useEffect.handleKeyDown\": (event)=>{\n                    if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {\n                        event.preventDefault();\n                        toggleSidebar();\n                    }\n                }\n            }[\"SidebarProvider.useEffect.handleKeyDown\"];\n            window.addEventListener(\"keydown\", handleKeyDown);\n            return ({\n                \"SidebarProvider.useEffect\": ()=>window.removeEventListener(\"keydown\", handleKeyDown)\n            })[\"SidebarProvider.useEffect\"];\n        }\n    }[\"SidebarProvider.useEffect\"], [\n        toggleSidebar\n    ]);\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? \"expanded\" : \"collapsed\";\n    const contextValue = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SidebarProvider.useMemo[contextValue]\": ()=>({\n                state,\n                open,\n                setOpen,\n                isMobile,\n                openMobile,\n                setOpenMobile,\n                toggleSidebar\n            })\n    }[\"SidebarProvider.useMemo[contextValue]\"], [\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipProvider, {\n            delayDuration: 0,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    \"--sidebar-width\": SIDEBAR_WIDTH,\n                    \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n                    ...style\n                },\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\", className),\n                ref: ref,\n                ...props,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 141,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 140,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 139,\n        columnNumber: 7\n    }, undefined);\n});\nSidebarProvider.displayName = \"SidebarProvider\";\nconst Sidebar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ side = \"left\", variant = \"sidebar\", collapsible = \"offcanvas\", className, children, ...props }, ref)=>{\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\n    if (collapsible === \"none\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\", className),\n            ref: ref,\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 188,\n            columnNumber: 9\n        }, undefined);\n    }\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.Sheet, {\n            open: openMobile,\n            onOpenChange: setOpenMobile,\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetContent, {\n                \"data-sidebar\": \"sidebar\",\n                \"data-mobile\": \"true\",\n                className: \"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\",\n                style: {\n                    \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE\n                },\n                side: side,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetHeader, {\n                        className: \"sr-only\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetTitle, {\n                                children: \"Sidebar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetDescription, {\n                                children: \"Displays the mobile sidebar.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-full w-full flex-col\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 204,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 203,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: \"group peer hidden text-sidebar-foreground md:block\",\n        \"data-state\": state,\n        \"data-collapsible\": state === \"collapsed\" ? collapsible : \"\",\n        \"data-variant\": variant,\n        \"data-side\": side,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative w-[--sidebar-width] bg-transparent transition-[width] duration-200 ease-linear\", \"group-data-[collapsible=offcanvas]:w-0\", \"group-data-[side=right]:rotate-180\", variant === \"floating\" || variant === \"inset\" ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\" : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] duration-200 ease-linear md:flex\", side === \"left\" ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\" : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\", // Adjust the padding for floating and inset variants.\n                variant === \"floating\" || variant === \"inset\" ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\" : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\", className),\n                ...props,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    \"data-sidebar\": \"sidebar\",\n                    className: \"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 226,\n        columnNumber: 7\n    }, undefined);\n});\nSidebar.displayName = \"Sidebar\";\nconst SidebarTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, onClick, ...props }, ref)=>{\n    const { toggleSidebar } = useSidebar();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n        ref: ref,\n        \"data-sidebar\": \"trigger\",\n        variant: \"ghost\",\n        size: \"icon\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"h-7 w-7\", className),\n        onClick: (event)=>{\n            onClick?.(event);\n            toggleSidebar();\n        },\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PanelLeft_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle Sidebar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarTrigger.displayName = \"SidebarTrigger\";\nconst SidebarRail = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    const { toggleSidebar } = useSidebar();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        \"data-sidebar\": \"rail\",\n        \"aria-label\": \"Toggle Sidebar\",\n        tabIndex: -1,\n        onClick: toggleSidebar,\n        title: \"Toggle Sidebar\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\", \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\", \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\", \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\", \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\", \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 305,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarRail.displayName = \"SidebarRail\";\nconst SidebarInset = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative flex w-full flex-1 flex-col bg-background\", \"md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 332,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarInset.displayName = \"SidebarInset\";\nconst SidebarInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n        ref: ref,\n        \"data-sidebar\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 350,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarInput.displayName = \"SidebarInput\";\nconst SidebarHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        \"data-sidebar\": \"header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex flex-col gap-2 p-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarHeader.displayName = \"SidebarHeader\";\nconst SidebarFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        \"data-sidebar\": \"footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex flex-col gap-2 p-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 383,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarFooter.displayName = \"SidebarFooter\";\nconst SidebarSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n        ref: ref,\n        \"data-sidebar\": \"separator\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"mx-2 w-auto bg-sidebar-border\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 398,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarSeparator.displayName = \"SidebarSeparator\";\nconst SidebarContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        \"data-sidebar\": \"content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 413,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarContent.displayName = \"SidebarContent\";\nconst SidebarGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        \"data-sidebar\": \"group\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative flex w-full min-w-0 flex-col p-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 431,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarGroup.displayName = \"SidebarGroup\";\nconst SidebarGroupLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slot : \"div\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        ref: ref,\n        \"data-sidebar\": \"group-label\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\", \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 448,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\";\nconst SidebarGroupAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        ref: ref,\n        \"data-sidebar\": \"group-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\", // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 469,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarGroupAction.displayName = \"SidebarGroupAction\";\nconst SidebarGroupContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        \"data-sidebar\": \"group-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 489,\n        columnNumber: 3\n    }, undefined));\nSidebarGroupContent.displayName = \"SidebarGroupContent\";\nconst SidebarMenu = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        ref: ref,\n        \"data-sidebar\": \"menu\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex w-full min-w-0 flex-col gap-1\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 502,\n        columnNumber: 3\n    }, undefined));\nSidebarMenu.displayName = \"SidebarMenu\";\nconst SidebarMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        ref: ref,\n        \"data-sidebar\": \"menu-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"group/menu-item relative\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 515,\n        columnNumber: 3\n    }, undefined));\nSidebarMenuItem.displayName = \"SidebarMenuItem\";\nconst sidebarMenuButtonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n            outline: \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\"\n        },\n        size: {\n            default: \"h-8 text-sm\",\n            sm: \"h-7 text-xs\",\n            lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst SidebarMenuButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ asChild = false, isActive = false, variant = \"default\", size = \"default\", tooltip, className, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slot : \"button\";\n    const { isMobile, state } = useSidebar();\n    const button = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        ref: ref,\n        \"data-sidebar\": \"menu-button\",\n        \"data-size\": size,\n        \"data-active\": isActive,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(sidebarMenuButtonVariants({\n            variant,\n            size\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 570,\n        columnNumber: 7\n    }, undefined);\n    if (!tooltip) {\n        return button;\n    }\n    if (typeof tooltip === \"string\") {\n        tooltip = {\n            children: tooltip\n        };\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                asChild: true,\n                children: button\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 592,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                side: \"right\",\n                align: \"center\",\n                hidden: state !== \"collapsed\" || isMobile,\n                ...tooltip\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 593,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 591,\n        columnNumber: 7\n    }, undefined);\n});\nSidebarMenuButton.displayName = \"SidebarMenuButton\";\nconst SidebarMenuAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, asChild = false, showOnHover = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        ref: ref,\n        \"data-sidebar\": \"menu-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\", // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\", \"peer-data-[size=sm]/menu-button:top-1\", \"peer-data-[size=default]/menu-button:top-1.5\", \"peer-data-[size=lg]/menu-button:top-2.5\", \"group-data-[collapsible=icon]:hidden\", showOnHover && \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 615,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarMenuAction.displayName = \"SidebarMenuAction\";\nconst SidebarMenuBadge = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        \"data-sidebar\": \"menu-badge\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"pointer-events-none absolute right-1 flex h-5 min-w-5 select-none items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground\", \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\", \"peer-data-[size=sm]/menu-button:top-1\", \"peer-data-[size=default]/menu-button:top-1.5\", \"peer-data-[size=lg]/menu-button:top-2.5\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 640,\n        columnNumber: 3\n    }, undefined));\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\";\nconst SidebarMenuSkeleton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, showIcon = false, ...props }, ref)=>{\n    // Random width between 50 to 90%.\n    const width = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SidebarMenuSkeleton.useMemo[width]\": ()=>{\n            return `${Math.floor(Math.random() * 40) + 50}%`;\n        }\n    }[\"SidebarMenuSkeleton.useMemo[width]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        \"data-sidebar\": \"menu-skeleton\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex h-8 items-center gap-2 rounded-md px-2\", className),\n        ...props,\n        children: [\n            showIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                className: \"size-4 rounded-md\",\n                \"data-sidebar\": \"menu-skeleton-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 676,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                className: \"h-4 max-w-[--skeleton-width] flex-1\",\n                \"data-sidebar\": \"menu-skeleton-text\",\n                style: {\n                    \"--skeleton-width\": width\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 681,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 669,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\";\nconst SidebarMenuSub = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        ref: ref,\n        \"data-sidebar\": \"menu-sub\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 699,\n        columnNumber: 3\n    }, undefined));\nSidebarMenuSub.displayName = \"SidebarMenuSub\";\nconst SidebarMenuSubItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 715,\n        columnNumber: 26\n    }, undefined));\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\";\nconst SidebarMenuSubButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ asChild = false, size = \"md\", isActive, className, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slot : \"a\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        ref: ref,\n        \"data-sidebar\": \"menu-sub-button\",\n        \"data-size\": size,\n        \"data-active\": isActive,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\", \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\", size === \"sm\" && \"text-xs\", size === \"md\" && \"text-sm\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 729,\n        columnNumber: 5\n    }, undefined);\n});\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9zaWRlYmFyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRThCO0FBQ2E7QUFDaUI7QUFDcEI7QUFFUTtBQUNoQjtBQUNlO0FBQ0Y7QUFDUTtBQU92QjtBQUNxQjtBQU1uQjtBQUVoQyxNQUFNbUIsc0JBQXNCO0FBQzVCLE1BQU1DLHlCQUF5QixLQUFLLEtBQUssS0FBSztBQUM5QyxNQUFNQyxnQkFBZ0I7QUFDdEIsTUFBTUMsdUJBQXVCO0FBQzdCLE1BQU1DLHFCQUFxQjtBQUMzQixNQUFNQyw0QkFBNEI7QUFZbEMsTUFBTUMsK0JBQWlCekIsZ0RBQW1CLENBQTZCO0FBRXZFLFNBQVMyQjtJQUNQLE1BQU1DLFVBQVU1Qiw2Q0FBZ0IsQ0FBQ3lCO0lBQ2pDLElBQUksQ0FBQ0csU0FBUztRQUNaLE1BQU0sSUFBSUUsTUFBTTtJQUNsQjtJQUVBLE9BQU9GO0FBQ1Q7QUFFQSxNQUFNRyxnQ0FBa0IvQiw2Q0FBZ0IsQ0FRdEMsQ0FDRSxFQUNFaUMsY0FBYyxJQUFJLEVBQ2xCQyxNQUFNQyxRQUFRLEVBQ2RDLGNBQWNDLFdBQVcsRUFDekJDLFNBQVMsRUFDVEMsS0FBSyxFQUNMQyxRQUFRLEVBQ1IsR0FBR0MsT0FDSixFQUNEQztJQUVBLE1BQU1DLFdBQVd2Qyw4REFBV0E7SUFDNUIsTUFBTSxDQUFDd0MsWUFBWUMsY0FBYyxHQUFHN0MsMkNBQWMsQ0FBQztJQUVuRCw2Q0FBNkM7SUFDN0MsMEVBQTBFO0lBQzFFLE1BQU0sQ0FBQytDLE9BQU9DLFNBQVMsR0FBR2hELDJDQUFjLENBQUNpQztJQUN6QyxNQUFNQyxPQUFPQyxZQUFZWTtJQUN6QixNQUFNRSxVQUFVakQsOENBQWlCO2dEQUMvQixDQUFDbUQ7WUFDQyxNQUFNQyxZQUFZLE9BQU9ELFVBQVUsYUFBYUEsTUFBTWpCLFFBQVFpQjtZQUM5RCxJQUFJZCxhQUFhO2dCQUNmQSxZQUFZZTtZQUNkLE9BQU87Z0JBQ0xKLFNBQVNJO1lBQ1g7WUFFQSxrREFBa0Q7WUFDbERDLFNBQVNDLE1BQU0sR0FBRyxHQUFHbkMsb0JBQW9CLENBQUMsRUFBRWlDLFVBQVUsa0JBQWtCLEVBQUVoQyx3QkFBd0I7UUFDcEc7K0NBQ0E7UUFBQ2lCO1FBQWFIO0tBQUs7SUFHckIsZ0NBQWdDO0lBQ2hDLE1BQU1xQixnQkFBZ0J2RCw4Q0FBaUI7c0RBQUM7WUFDdEMsT0FBTzJDLFdBQ0hFOzhEQUFjLENBQUNYLE9BQVMsQ0FBQ0E7K0RBQ3pCZTs4REFBUSxDQUFDZixPQUFTLENBQUNBOztRQUN6QjtxREFBRztRQUFDUztRQUFVTTtRQUFTSjtLQUFjO0lBRXJDLGtEQUFrRDtJQUNsRDdDLDRDQUFlO3FDQUFDO1lBQ2QsTUFBTXlEOzJEQUFnQixDQUFDQztvQkFDckIsSUFDRUEsTUFBTUMsR0FBRyxLQUFLbkMsNkJBQ2JrQyxDQUFBQSxNQUFNRSxPQUFPLElBQUlGLE1BQU1HLE9BQU8sR0FDL0I7d0JBQ0FILE1BQU1JLGNBQWM7d0JBQ3BCUDtvQkFDRjtnQkFDRjs7WUFFQVEsT0FBT0MsZ0JBQWdCLENBQUMsV0FBV1A7WUFDbkM7NkNBQU8sSUFBTU0sT0FBT0UsbUJBQW1CLENBQUMsV0FBV1I7O1FBQ3JEO29DQUFHO1FBQUNGO0tBQWM7SUFFbEIseUVBQXlFO0lBQ3pFLG1FQUFtRTtJQUNuRSxNQUFNVyxRQUFRaEMsT0FBTyxhQUFhO0lBRWxDLE1BQU1pQyxlQUFlbkUsMENBQWE7aURBQ2hDLElBQU87Z0JBQ0xrRTtnQkFDQWhDO2dCQUNBZTtnQkFDQU47Z0JBQ0FDO2dCQUNBQztnQkFDQVU7WUFDRjtnREFDQTtRQUFDVztRQUFPaEM7UUFBTWU7UUFBU047UUFBVUM7UUFBWUM7UUFBZVU7S0FBYztJQUc1RSxxQkFDRSw4REFBQzlCLGVBQWU0QyxRQUFRO1FBQUNsQixPQUFPZ0I7a0JBQzlCLDRFQUFDbEQsb0VBQWVBO1lBQUNxRCxlQUFlO3NCQUM5Qiw0RUFBQ0M7Z0JBQ0NoQyxPQUNFO29CQUNFLG1CQUFtQmxCO29CQUNuQix3QkFBd0JFO29CQUN4QixHQUFHZ0IsS0FBSztnQkFDVjtnQkFFRkQsV0FBV2pDLDhDQUFFQSxDQUNYLHFGQUNBaUM7Z0JBRUZJLEtBQUtBO2dCQUNKLEdBQUdELEtBQUs7MEJBRVJEOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1g7QUFFRlQsZ0JBQWdCeUMsV0FBVyxHQUFHO0FBRTlCLE1BQU1DLHdCQUFVekUsNkNBQWdCLENBUTlCLENBQ0UsRUFDRTBFLE9BQU8sTUFBTSxFQUNiQyxVQUFVLFNBQVMsRUFDbkJDLGNBQWMsV0FBVyxFQUN6QnRDLFNBQVMsRUFDVEUsUUFBUSxFQUNSLEdBQUdDLE9BQ0osRUFDREM7SUFFQSxNQUFNLEVBQUVDLFFBQVEsRUFBRXVCLEtBQUssRUFBRXRCLFVBQVUsRUFBRUMsYUFBYSxFQUFFLEdBQUdsQjtJQUV2RCxJQUFJaUQsZ0JBQWdCLFFBQVE7UUFDMUIscUJBQ0UsOERBQUNMO1lBQ0NqQyxXQUFXakMsOENBQUVBLENBQ1gsK0VBQ0FpQztZQUVGSSxLQUFLQTtZQUNKLEdBQUdELEtBQUs7c0JBRVJEOzs7Ozs7SUFHUDtJQUVBLElBQUlHLFVBQVU7UUFDWixxQkFDRSw4REFBQ2xDLHVEQUFLQTtZQUFDeUIsTUFBTVU7WUFBWVIsY0FBY1M7WUFBZ0IsR0FBR0osS0FBSztzQkFDN0QsNEVBQUMvQiw4REFBWUE7Z0JBQ1htRSxnQkFBYTtnQkFDYkMsZUFBWTtnQkFDWnhDLFdBQVU7Z0JBQ1ZDLE9BQ0U7b0JBQ0UsbUJBQW1CakI7Z0JBQ3JCO2dCQUVGb0QsTUFBTUE7O2tDQUVOLDhEQUFDOUQsNkRBQVdBO3dCQUFDMEIsV0FBVTs7MENBQ3JCLDhEQUFDekIsNERBQVVBOzBDQUFDOzs7Ozs7MENBQ1osOERBQUNGLGtFQUFnQkE7MENBQUM7Ozs7Ozs7Ozs7OztrQ0FFcEIsOERBQUM0RDt3QkFBSWpDLFdBQVU7a0NBQStCRTs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJdEQ7SUFFQSxxQkFDRSw4REFBQytCO1FBQ0M3QixLQUFLQTtRQUNMSixXQUFVO1FBQ1Z5QyxjQUFZYjtRQUNaYyxvQkFBa0JkLFVBQVUsY0FBY1UsY0FBYztRQUN4REssZ0JBQWNOO1FBQ2RPLGFBQVdSOzswQkFHWCw4REFBQ0g7Z0JBQ0NqQyxXQUFXakMsOENBQUVBLENBQ1gsMkZBQ0EsMENBQ0Esc0NBQ0FzRSxZQUFZLGNBQWNBLFlBQVksVUFDbEMseUZBQ0E7Ozs7OzswQkFHUiw4REFBQ0o7Z0JBQ0NqQyxXQUFXakMsOENBQUVBLENBQ1gsd0hBQ0FxRSxTQUFTLFNBQ0wsbUZBQ0Esb0ZBQ0osc0RBQXNEO2dCQUN0REMsWUFBWSxjQUFjQSxZQUFZLFVBQ2xDLGtHQUNBLDJIQUNKckM7Z0JBRUQsR0FBR0csS0FBSzswQkFFVCw0RUFBQzhCO29CQUNDTSxnQkFBYTtvQkFDYnZDLFdBQVU7OEJBRVRFOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYO0FBRUZpQyxRQUFRRCxXQUFXLEdBQUc7QUFFdEIsTUFBTVcsK0JBQWlCbkYsNkNBQWdCLENBR3JDLENBQUMsRUFBRXNDLFNBQVMsRUFBRThDLE9BQU8sRUFBRSxHQUFHM0MsT0FBTyxFQUFFQztJQUNuQyxNQUFNLEVBQUVhLGFBQWEsRUFBRSxHQUFHNUI7SUFFMUIscUJBQ0UsOERBQUNyQix5REFBTUE7UUFDTG9DLEtBQUtBO1FBQ0xtQyxnQkFBYTtRQUNiRixTQUFRO1FBQ1JVLE1BQUs7UUFDTC9DLFdBQVdqQyw4Q0FBRUEsQ0FBQyxXQUFXaUM7UUFDekI4QyxTQUFTLENBQUMxQjtZQUNSMEIsVUFBVTFCO1lBQ1ZIO1FBQ0Y7UUFDQyxHQUFHZCxLQUFLOzswQkFFVCw4REFBQ3RDLHNGQUFTQTs7Ozs7MEJBQ1YsOERBQUNtRjtnQkFBS2hELFdBQVU7MEJBQVU7Ozs7Ozs7Ozs7OztBQUdoQztBQUNBNkMsZUFBZVgsV0FBVyxHQUFHO0FBRTdCLE1BQU1lLDRCQUFjdkYsNkNBQWdCLENBR2xDLENBQUMsRUFBRXNDLFNBQVMsRUFBRSxHQUFHRyxPQUFPLEVBQUVDO0lBQzFCLE1BQU0sRUFBRWEsYUFBYSxFQUFFLEdBQUc1QjtJQUUxQixxQkFDRSw4REFBQzZEO1FBQ0M5QyxLQUFLQTtRQUNMbUMsZ0JBQWE7UUFDYlksY0FBVztRQUNYQyxVQUFVLENBQUM7UUFDWE4sU0FBUzdCO1FBQ1RvQyxPQUFNO1FBQ05yRCxXQUFXakMsOENBQUVBLENBQ1gsbVBBQ0EsOEVBQ0EsMEhBQ0EsMkpBQ0EsNkRBQ0EsNkRBQ0FpQztRQUVELEdBQUdHLEtBQUs7Ozs7OztBQUdmO0FBQ0E4QyxZQUFZZixXQUFXLEdBQUc7QUFFMUIsTUFBTW9CLDZCQUFlNUYsNkNBQWdCLENBR25DLENBQUMsRUFBRXNDLFNBQVMsRUFBRSxHQUFHRyxPQUFPLEVBQUVDO0lBQzFCLHFCQUNFLDhEQUFDbUQ7UUFDQ25ELEtBQUtBO1FBQ0xKLFdBQVdqQyw4Q0FBRUEsQ0FDWCxzREFDQSxnTkFDQWlDO1FBRUQsR0FBR0csS0FBSzs7Ozs7O0FBR2Y7QUFDQW1ELGFBQWFwQixXQUFXLEdBQUc7QUFFM0IsTUFBTXNCLDZCQUFlOUYsNkNBQWdCLENBR25DLENBQUMsRUFBRXNDLFNBQVMsRUFBRSxHQUFHRyxPQUFPLEVBQUVDO0lBQzFCLHFCQUNFLDhEQUFDbkMsdURBQUtBO1FBQ0ptQyxLQUFLQTtRQUNMbUMsZ0JBQWE7UUFDYnZDLFdBQVdqQyw4Q0FBRUEsQ0FDWCw2RkFDQWlDO1FBRUQsR0FBR0csS0FBSzs7Ozs7O0FBR2Y7QUFDQXFELGFBQWF0QixXQUFXLEdBQUc7QUFFM0IsTUFBTXVCLDhCQUFnQi9GLDZDQUFnQixDQUdwQyxDQUFDLEVBQUVzQyxTQUFTLEVBQUUsR0FBR0csT0FBTyxFQUFFQztJQUMxQixxQkFDRSw4REFBQzZCO1FBQ0M3QixLQUFLQTtRQUNMbUMsZ0JBQWE7UUFDYnZDLFdBQVdqQyw4Q0FBRUEsQ0FBQywyQkFBMkJpQztRQUN4QyxHQUFHRyxLQUFLOzs7Ozs7QUFHZjtBQUNBc0QsY0FBY3ZCLFdBQVcsR0FBRztBQUU1QixNQUFNd0IsOEJBQWdCaEcsNkNBQWdCLENBR3BDLENBQUMsRUFBRXNDLFNBQVMsRUFBRSxHQUFHRyxPQUFPLEVBQUVDO0lBQzFCLHFCQUNFLDhEQUFDNkI7UUFDQzdCLEtBQUtBO1FBQ0xtQyxnQkFBYTtRQUNidkMsV0FBV2pDLDhDQUFFQSxDQUFDLDJCQUEyQmlDO1FBQ3hDLEdBQUdHLEtBQUs7Ozs7OztBQUdmO0FBQ0F1RCxjQUFjeEIsV0FBVyxHQUFHO0FBRTVCLE1BQU15QixpQ0FBbUJqRyw2Q0FBZ0IsQ0FHdkMsQ0FBQyxFQUFFc0MsU0FBUyxFQUFFLEdBQUdHLE9BQU8sRUFBRUM7SUFDMUIscUJBQ0UsOERBQUNsQywrREFBU0E7UUFDUmtDLEtBQUtBO1FBQ0xtQyxnQkFBYTtRQUNidkMsV0FBV2pDLDhDQUFFQSxDQUFDLGlDQUFpQ2lDO1FBQzlDLEdBQUdHLEtBQUs7Ozs7OztBQUdmO0FBQ0F3RCxpQkFBaUJ6QixXQUFXLEdBQUc7QUFFL0IsTUFBTTBCLCtCQUFpQmxHLDZDQUFnQixDQUdyQyxDQUFDLEVBQUVzQyxTQUFTLEVBQUUsR0FBR0csT0FBTyxFQUFFQztJQUMxQixxQkFDRSw4REFBQzZCO1FBQ0M3QixLQUFLQTtRQUNMbUMsZ0JBQWE7UUFDYnZDLFdBQVdqQyw4Q0FBRUEsQ0FDWCxrR0FDQWlDO1FBRUQsR0FBR0csS0FBSzs7Ozs7O0FBR2Y7QUFDQXlELGVBQWUxQixXQUFXLEdBQUc7QUFFN0IsTUFBTTJCLDZCQUFlbkcsNkNBQWdCLENBR25DLENBQUMsRUFBRXNDLFNBQVMsRUFBRSxHQUFHRyxPQUFPLEVBQUVDO0lBQzFCLHFCQUNFLDhEQUFDNkI7UUFDQzdCLEtBQUtBO1FBQ0xtQyxnQkFBYTtRQUNidkMsV0FBV2pDLDhDQUFFQSxDQUFDLDZDQUE2Q2lDO1FBQzFELEdBQUdHLEtBQUs7Ozs7OztBQUdmO0FBQ0EwRCxhQUFhM0IsV0FBVyxHQUFHO0FBRTNCLE1BQU00QixrQ0FBb0JwRyw2Q0FBZ0IsQ0FHeEMsQ0FBQyxFQUFFc0MsU0FBUyxFQUFFK0QsVUFBVSxLQUFLLEVBQUUsR0FBRzVELE9BQU8sRUFBRUM7SUFDM0MsTUFBTTRELE9BQU9ELFVBQVVwRyx1REFBSUEsR0FBRztJQUU5QixxQkFDRSw4REFBQ3FHO1FBQ0M1RCxLQUFLQTtRQUNMbUMsZ0JBQWE7UUFDYnZDLFdBQVdqQyw4Q0FBRUEsQ0FDWCwwT0FDQSwrRUFDQWlDO1FBRUQsR0FBR0csS0FBSzs7Ozs7O0FBR2Y7QUFDQTJELGtCQUFrQjVCLFdBQVcsR0FBRztBQUVoQyxNQUFNK0IsbUNBQXFCdkcsNkNBQWdCLENBR3pDLENBQUMsRUFBRXNDLFNBQVMsRUFBRStELFVBQVUsS0FBSyxFQUFFLEdBQUc1RCxPQUFPLEVBQUVDO0lBQzNDLE1BQU00RCxPQUFPRCxVQUFVcEcsdURBQUlBLEdBQUc7SUFFOUIscUJBQ0UsOERBQUNxRztRQUNDNUQsS0FBS0E7UUFDTG1DLGdCQUFhO1FBQ2J2QyxXQUFXakMsOENBQUVBLENBQ1gsNFJBQ0Esa0RBQWtEO1FBQ2xELGlEQUNBLHdDQUNBaUM7UUFFRCxHQUFHRyxLQUFLOzs7Ozs7QUFHZjtBQUNBOEQsbUJBQW1CL0IsV0FBVyxHQUFHO0FBRWpDLE1BQU1nQyxvQ0FBc0J4Ryw2Q0FBZ0IsQ0FHMUMsQ0FBQyxFQUFFc0MsU0FBUyxFQUFFLEdBQUdHLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDNkI7UUFDQzdCLEtBQUtBO1FBQ0xtQyxnQkFBYTtRQUNidkMsV0FBV2pDLDhDQUFFQSxDQUFDLGtCQUFrQmlDO1FBQy9CLEdBQUdHLEtBQUs7Ozs7OztBQUdiK0Qsb0JBQW9CaEMsV0FBVyxHQUFHO0FBRWxDLE1BQU1pQyw0QkFBY3pHLDZDQUFnQixDQUdsQyxDQUFDLEVBQUVzQyxTQUFTLEVBQUUsR0FBR0csT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNnRTtRQUNDaEUsS0FBS0E7UUFDTG1DLGdCQUFhO1FBQ2J2QyxXQUFXakMsOENBQUVBLENBQUMsc0NBQXNDaUM7UUFDbkQsR0FBR0csS0FBSzs7Ozs7O0FBR2JnRSxZQUFZakMsV0FBVyxHQUFHO0FBRTFCLE1BQU1tQyxnQ0FBa0IzRyw2Q0FBZ0IsQ0FHdEMsQ0FBQyxFQUFFc0MsU0FBUyxFQUFFLEdBQUdHLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDa0U7UUFDQ2xFLEtBQUtBO1FBQ0xtQyxnQkFBYTtRQUNidkMsV0FBV2pDLDhDQUFFQSxDQUFDLDRCQUE0QmlDO1FBQ3pDLEdBQUdHLEtBQUs7Ozs7OztBQUdia0UsZ0JBQWdCbkMsV0FBVyxHQUFHO0FBRTlCLE1BQU1xQyw0QkFBNEIzRyw2REFBR0EsQ0FDbkMscXpCQUNBO0lBQ0U0RyxVQUFVO1FBQ1JuQyxTQUFTO1lBQ1BvQyxTQUFTO1lBQ1RDLFNBQ0U7UUFDSjtRQUNBM0IsTUFBTTtZQUNKMEIsU0FBUztZQUNURSxJQUFJO1lBQ0pDLElBQUk7UUFDTjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmeEMsU0FBUztRQUNUVSxNQUFNO0lBQ1I7QUFDRjtBQUdGLE1BQU0rQixrQ0FBb0JwSCw2Q0FBZ0IsQ0FReEMsQ0FDRSxFQUNFcUcsVUFBVSxLQUFLLEVBQ2ZnQixXQUFXLEtBQUssRUFDaEIxQyxVQUFVLFNBQVMsRUFDbkJVLE9BQU8sU0FBUyxFQUNoQmlDLE9BQU8sRUFDUGhGLFNBQVMsRUFDVCxHQUFHRyxPQUNKLEVBQ0RDO0lBRUEsTUFBTTRELE9BQU9ELFVBQVVwRyx1REFBSUEsR0FBRztJQUM5QixNQUFNLEVBQUUwQyxRQUFRLEVBQUV1QixLQUFLLEVBQUUsR0FBR3ZDO0lBRTVCLE1BQU02RCx1QkFDSiw4REFBQ2M7UUFDQzVELEtBQUtBO1FBQ0xtQyxnQkFBYTtRQUNiMEMsYUFBV2xDO1FBQ1htQyxlQUFhSDtRQUNiL0UsV0FBV2pDLDhDQUFFQSxDQUFDd0csMEJBQTBCO1lBQUVsQztZQUFTVTtRQUFLLElBQUkvQztRQUMzRCxHQUFHRyxLQUFLOzs7Ozs7SUFJYixJQUFJLENBQUM2RSxTQUFTO1FBQ1osT0FBTzlCO0lBQ1Q7SUFFQSxJQUFJLE9BQU84QixZQUFZLFVBQVU7UUFDL0JBLFVBQVU7WUFDUjlFLFVBQVU4RTtRQUNaO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3ZHLDREQUFPQTs7MEJBQ04sOERBQUNHLG1FQUFjQTtnQkFBQ21GLE9BQU87MEJBQUViOzs7Ozs7MEJBQ3pCLDhEQUFDeEUsbUVBQWNBO2dCQUNiMEQsTUFBSztnQkFDTCtDLE9BQU07Z0JBQ05DLFFBQVF4RCxVQUFVLGVBQWV2QjtnQkFDaEMsR0FBRzJFLE9BQU87Ozs7Ozs7Ozs7OztBQUluQjtBQUVGRixrQkFBa0I1QyxXQUFXLEdBQUc7QUFFaEMsTUFBTW1ELGtDQUFvQjNILDZDQUFnQixDQU14QyxDQUFDLEVBQUVzQyxTQUFTLEVBQUUrRCxVQUFVLEtBQUssRUFBRXVCLGNBQWMsS0FBSyxFQUFFLEdBQUduRixPQUFPLEVBQUVDO0lBQ2hFLE1BQU00RCxPQUFPRCxVQUFVcEcsdURBQUlBLEdBQUc7SUFFOUIscUJBQ0UsOERBQUNxRztRQUNDNUQsS0FBS0E7UUFDTG1DLGdCQUFhO1FBQ2J2QyxXQUFXakMsOENBQUVBLENBQ1gsa1ZBQ0Esa0RBQWtEO1FBQ2xELGlEQUNBLHlDQUNBLGdEQUNBLDJDQUNBLHdDQUNBdUgsZUFDRSw0TEFDRnRGO1FBRUQsR0FBR0csS0FBSzs7Ozs7O0FBR2Y7QUFDQWtGLGtCQUFrQm5ELFdBQVcsR0FBRztBQUVoQyxNQUFNcUQsaUNBQW1CN0gsNkNBQWdCLENBR3ZDLENBQUMsRUFBRXNDLFNBQVMsRUFBRSxHQUFHRyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQzZCO1FBQ0M3QixLQUFLQTtRQUNMbUMsZ0JBQWE7UUFDYnZDLFdBQVdqQyw4Q0FBRUEsQ0FDWCwwS0FDQSw0SEFDQSx5Q0FDQSxnREFDQSwyQ0FDQSx3Q0FDQWlDO1FBRUQsR0FBR0csS0FBSzs7Ozs7O0FBR2JvRixpQkFBaUJyRCxXQUFXLEdBQUc7QUFFL0IsTUFBTXNELG9DQUFzQjlILDZDQUFnQixDQUsxQyxDQUFDLEVBQUVzQyxTQUFTLEVBQUV5RixXQUFXLEtBQUssRUFBRSxHQUFHdEYsT0FBTyxFQUFFQztJQUM1QyxrQ0FBa0M7SUFDbEMsTUFBTXNGLFFBQVFoSSwwQ0FBYTs4Q0FBQztZQUMxQixPQUFPLEdBQUdpSSxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSyxNQUFNLEdBQUcsQ0FBQyxDQUFDO1FBQ2xEOzZDQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQzVEO1FBQ0M3QixLQUFLQTtRQUNMbUMsZ0JBQWE7UUFDYnZDLFdBQVdqQyw4Q0FBRUEsQ0FBQywrQ0FBK0NpQztRQUM1RCxHQUFHRyxLQUFLOztZQUVSc0YsMEJBQ0MsOERBQUNqSCw2REFBUUE7Z0JBQ1B3QixXQUFVO2dCQUNWdUMsZ0JBQWE7Ozs7OzswQkFHakIsOERBQUMvRCw2REFBUUE7Z0JBQ1B3QixXQUFVO2dCQUNWdUMsZ0JBQWE7Z0JBQ2J0QyxPQUNFO29CQUNFLG9CQUFvQnlGO2dCQUN0Qjs7Ozs7Ozs7Ozs7O0FBS1Y7QUFDQUYsb0JBQW9CdEQsV0FBVyxHQUFHO0FBRWxDLE1BQU00RCwrQkFBaUJwSSw2Q0FBZ0IsQ0FHckMsQ0FBQyxFQUFFc0MsU0FBUyxFQUFFLEdBQUdHLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDZ0U7UUFDQ2hFLEtBQUtBO1FBQ0xtQyxnQkFBYTtRQUNidkMsV0FBV2pDLDhDQUFFQSxDQUNYLGtHQUNBLHdDQUNBaUM7UUFFRCxHQUFHRyxLQUFLOzs7Ozs7QUFHYjJGLGVBQWU1RCxXQUFXLEdBQUc7QUFFN0IsTUFBTTZELG1DQUFxQnJJLDZDQUFnQixDQUd6QyxDQUFDLEVBQUUsR0FBR3lDLE9BQU8sRUFBRUMsb0JBQVEsOERBQUNrRTtRQUFHbEUsS0FBS0E7UUFBTSxHQUFHRCxLQUFLOzs7Ozs7QUFDaEQ0RixtQkFBbUI3RCxXQUFXLEdBQUc7QUFFakMsTUFBTThELHFDQUF1QnRJLDZDQUFnQixDQU8zQyxDQUFDLEVBQUVxRyxVQUFVLEtBQUssRUFBRWhCLE9BQU8sSUFBSSxFQUFFZ0MsUUFBUSxFQUFFL0UsU0FBUyxFQUFFLEdBQUdHLE9BQU8sRUFBRUM7SUFDbEUsTUFBTTRELE9BQU9ELFVBQVVwRyx1REFBSUEsR0FBRztJQUU5QixxQkFDRSw4REFBQ3FHO1FBQ0M1RCxLQUFLQTtRQUNMbUMsZ0JBQWE7UUFDYjBDLGFBQVdsQztRQUNYbUMsZUFBYUg7UUFDYi9FLFdBQVdqQyw4Q0FBRUEsQ0FDWCwrZUFDQSwwRkFDQWdGLFNBQVMsUUFBUSxXQUNqQkEsU0FBUyxRQUFRLFdBQ2pCLHdDQUNBL0M7UUFFRCxHQUFHRyxLQUFLOzs7Ozs7QUFHZjtBQUNBNkYscUJBQXFCOUQsV0FBVyxHQUFHO0FBMkJsQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXFZlbG9jaXR5XFxzdHVkeWJ1ZGR5LWZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxzaWRlYmFyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgU2xvdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3Qtc2xvdFwiXG5pbXBvcnQgeyBWYXJpYW50UHJvcHMsIGN2YSB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuaW1wb3J0IHsgUGFuZWxMZWZ0IH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5cbmltcG9ydCB7IHVzZUlzTW9iaWxlIH0gZnJvbSBcIkAvaG9va3MvdXNlLW1vYmlsZVwiXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIlxuaW1wb3J0IHsgU2VwYXJhdG9yIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9zZXBhcmF0b3JcIlxuaW1wb3J0IHtcbiAgU2hlZXQsXG4gIFNoZWV0Q29udGVudCxcbiAgU2hlZXREZXNjcmlwdGlvbixcbiAgU2hlZXRIZWFkZXIsXG4gIFNoZWV0VGl0bGUsXG59IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc2hlZXRcIlxuaW1wb3J0IHsgU2tlbGV0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NrZWxldG9uXCJcbmltcG9ydCB7XG4gIFRvb2x0aXAsXG4gIFRvb2x0aXBDb250ZW50LFxuICBUb29sdGlwUHJvdmlkZXIsXG4gIFRvb2x0aXBUcmlnZ2VyLFxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3Rvb2x0aXBcIlxuXG5jb25zdCBTSURFQkFSX0NPT0tJRV9OQU1FID0gXCJzaWRlYmFyX3N0YXRlXCJcbmNvbnN0IFNJREVCQVJfQ09PS0lFX01BWF9BR0UgPSA2MCAqIDYwICogMjQgKiA3XG5jb25zdCBTSURFQkFSX1dJRFRIID0gXCIxNnJlbVwiXG5jb25zdCBTSURFQkFSX1dJRFRIX01PQklMRSA9IFwiMThyZW1cIlxuY29uc3QgU0lERUJBUl9XSURUSF9JQ09OID0gXCIzcmVtXCJcbmNvbnN0IFNJREVCQVJfS0VZQk9BUkRfU0hPUlRDVVQgPSBcImJcIlxuXG50eXBlIFNpZGViYXJDb250ZXh0UHJvcHMgPSB7XG4gIHN0YXRlOiBcImV4cGFuZGVkXCIgfCBcImNvbGxhcHNlZFwiXG4gIG9wZW46IGJvb2xlYW5cbiAgc2V0T3BlbjogKG9wZW46IGJvb2xlYW4pID0+IHZvaWRcbiAgb3Blbk1vYmlsZTogYm9vbGVhblxuICBzZXRPcGVuTW9iaWxlOiAob3BlbjogYm9vbGVhbikgPT4gdm9pZFxuICBpc01vYmlsZTogYm9vbGVhblxuICB0b2dnbGVTaWRlYmFyOiAoKSA9PiB2b2lkXG59XG5cbmNvbnN0IFNpZGViYXJDb250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dDxTaWRlYmFyQ29udGV4dFByb3BzIHwgbnVsbD4obnVsbClcblxuZnVuY3Rpb24gdXNlU2lkZWJhcigpIHtcbiAgY29uc3QgY29udGV4dCA9IFJlYWN0LnVzZUNvbnRleHQoU2lkZWJhckNvbnRleHQpXG4gIGlmICghY29udGV4dCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcInVzZVNpZGViYXIgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFNpZGViYXJQcm92aWRlci5cIilcbiAgfVxuXG4gIHJldHVybiBjb250ZXh0XG59XG5cbmNvbnN0IFNpZGViYXJQcm92aWRlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPiAmIHtcbiAgICBkZWZhdWx0T3Blbj86IGJvb2xlYW5cbiAgICBvcGVuPzogYm9vbGVhblxuICAgIG9uT3BlbkNoYW5nZT86IChvcGVuOiBib29sZWFuKSA9PiB2b2lkXG4gIH1cbj4oXG4gIChcbiAgICB7XG4gICAgICBkZWZhdWx0T3BlbiA9IHRydWUsXG4gICAgICBvcGVuOiBvcGVuUHJvcCxcbiAgICAgIG9uT3BlbkNoYW5nZTogc2V0T3BlblByb3AsXG4gICAgICBjbGFzc05hbWUsXG4gICAgICBzdHlsZSxcbiAgICAgIGNoaWxkcmVuLFxuICAgICAgLi4ucHJvcHNcbiAgICB9LFxuICAgIHJlZlxuICApID0+IHtcbiAgICBjb25zdCBpc01vYmlsZSA9IHVzZUlzTW9iaWxlKClcbiAgICBjb25zdCBbb3Blbk1vYmlsZSwgc2V0T3Blbk1vYmlsZV0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSlcblxuICAgIC8vIFRoaXMgaXMgdGhlIGludGVybmFsIHN0YXRlIG9mIHRoZSBzaWRlYmFyLlxuICAgIC8vIFdlIHVzZSBvcGVuUHJvcCBhbmQgc2V0T3BlblByb3AgZm9yIGNvbnRyb2wgZnJvbSBvdXRzaWRlIHRoZSBjb21wb25lbnQuXG4gICAgY29uc3QgW19vcGVuLCBfc2V0T3Blbl0gPSBSZWFjdC51c2VTdGF0ZShkZWZhdWx0T3BlbilcbiAgICBjb25zdCBvcGVuID0gb3BlblByb3AgPz8gX29wZW5cbiAgICBjb25zdCBzZXRPcGVuID0gUmVhY3QudXNlQ2FsbGJhY2soXG4gICAgICAodmFsdWU6IGJvb2xlYW4gfCAoKHZhbHVlOiBib29sZWFuKSA9PiBib29sZWFuKSkgPT4ge1xuICAgICAgICBjb25zdCBvcGVuU3RhdGUgPSB0eXBlb2YgdmFsdWUgPT09IFwiZnVuY3Rpb25cIiA/IHZhbHVlKG9wZW4pIDogdmFsdWVcbiAgICAgICAgaWYgKHNldE9wZW5Qcm9wKSB7XG4gICAgICAgICAgc2V0T3BlblByb3Aob3BlblN0YXRlKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIF9zZXRPcGVuKG9wZW5TdGF0ZSlcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFRoaXMgc2V0cyB0aGUgY29va2llIHRvIGtlZXAgdGhlIHNpZGViYXIgc3RhdGUuXG4gICAgICAgIGRvY3VtZW50LmNvb2tpZSA9IGAke1NJREVCQVJfQ09PS0lFX05BTUV9PSR7b3BlblN0YXRlfTsgcGF0aD0vOyBtYXgtYWdlPSR7U0lERUJBUl9DT09LSUVfTUFYX0FHRX1gXG4gICAgICB9LFxuICAgICAgW3NldE9wZW5Qcm9wLCBvcGVuXVxuICAgIClcblxuICAgIC8vIEhlbHBlciB0byB0b2dnbGUgdGhlIHNpZGViYXIuXG4gICAgY29uc3QgdG9nZ2xlU2lkZWJhciA9IFJlYWN0LnVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICAgIHJldHVybiBpc01vYmlsZVxuICAgICAgICA/IHNldE9wZW5Nb2JpbGUoKG9wZW4pID0+ICFvcGVuKVxuICAgICAgICA6IHNldE9wZW4oKG9wZW4pID0+ICFvcGVuKVxuICAgIH0sIFtpc01vYmlsZSwgc2V0T3Blbiwgc2V0T3Blbk1vYmlsZV0pXG5cbiAgICAvLyBBZGRzIGEga2V5Ym9hcmQgc2hvcnRjdXQgdG8gdG9nZ2xlIHRoZSBzaWRlYmFyLlxuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGV2ZW50OiBLZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgICAgIGlmIChcbiAgICAgICAgICBldmVudC5rZXkgPT09IFNJREVCQVJfS0VZQk9BUkRfU0hPUlRDVVQgJiZcbiAgICAgICAgICAoZXZlbnQubWV0YUtleSB8fCBldmVudC5jdHJsS2V5KVxuICAgICAgICApIHtcbiAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpXG4gICAgICAgICAgdG9nZ2xlU2lkZWJhcigpXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24pXG4gICAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24pXG4gICAgfSwgW3RvZ2dsZVNpZGViYXJdKVxuXG4gICAgLy8gV2UgYWRkIGEgc3RhdGUgc28gdGhhdCB3ZSBjYW4gZG8gZGF0YS1zdGF0ZT1cImV4cGFuZGVkXCIgb3IgXCJjb2xsYXBzZWRcIi5cbiAgICAvLyBUaGlzIG1ha2VzIGl0IGVhc2llciB0byBzdHlsZSB0aGUgc2lkZWJhciB3aXRoIFRhaWx3aW5kIGNsYXNzZXMuXG4gICAgY29uc3Qgc3RhdGUgPSBvcGVuID8gXCJleHBhbmRlZFwiIDogXCJjb2xsYXBzZWRcIlxuXG4gICAgY29uc3QgY29udGV4dFZhbHVlID0gUmVhY3QudXNlTWVtbzxTaWRlYmFyQ29udGV4dFByb3BzPihcbiAgICAgICgpID0+ICh7XG4gICAgICAgIHN0YXRlLFxuICAgICAgICBvcGVuLFxuICAgICAgICBzZXRPcGVuLFxuICAgICAgICBpc01vYmlsZSxcbiAgICAgICAgb3Blbk1vYmlsZSxcbiAgICAgICAgc2V0T3Blbk1vYmlsZSxcbiAgICAgICAgdG9nZ2xlU2lkZWJhcixcbiAgICAgIH0pLFxuICAgICAgW3N0YXRlLCBvcGVuLCBzZXRPcGVuLCBpc01vYmlsZSwgb3Blbk1vYmlsZSwgc2V0T3Blbk1vYmlsZSwgdG9nZ2xlU2lkZWJhcl1cbiAgICApXG5cbiAgICByZXR1cm4gKFxuICAgICAgPFNpZGViYXJDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXtjb250ZXh0VmFsdWV9PlxuICAgICAgICA8VG9vbHRpcFByb3ZpZGVyIGRlbGF5RHVyYXRpb249ezB9PlxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIHN0eWxlPXtcbiAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIFwiLS1zaWRlYmFyLXdpZHRoXCI6IFNJREVCQVJfV0lEVEgsXG4gICAgICAgICAgICAgICAgXCItLXNpZGViYXItd2lkdGgtaWNvblwiOiBTSURFQkFSX1dJRFRIX0lDT04sXG4gICAgICAgICAgICAgICAgLi4uc3R5bGUsXG4gICAgICAgICAgICAgIH0gYXMgUmVhY3QuQ1NTUHJvcGVydGllc1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgXCJncm91cC9zaWRlYmFyLXdyYXBwZXIgZmxleCBtaW4taC1zdmggdy1mdWxsIGhhcy1bW2RhdGEtdmFyaWFudD1pbnNldF1dOmJnLXNpZGViYXJcIixcbiAgICAgICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgICAgPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L1Rvb2x0aXBQcm92aWRlcj5cbiAgICAgIDwvU2lkZWJhckNvbnRleHQuUHJvdmlkZXI+XG4gICAgKVxuICB9XG4pXG5TaWRlYmFyUHJvdmlkZXIuZGlzcGxheU5hbWUgPSBcIlNpZGViYXJQcm92aWRlclwiXG5cbmNvbnN0IFNpZGViYXIgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJkaXZcIj4gJiB7XG4gICAgc2lkZT86IFwibGVmdFwiIHwgXCJyaWdodFwiXG4gICAgdmFyaWFudD86IFwic2lkZWJhclwiIHwgXCJmbG9hdGluZ1wiIHwgXCJpbnNldFwiXG4gICAgY29sbGFwc2libGU/OiBcIm9mZmNhbnZhc1wiIHwgXCJpY29uXCIgfCBcIm5vbmVcIlxuICB9XG4+KFxuICAoXG4gICAge1xuICAgICAgc2lkZSA9IFwibGVmdFwiLFxuICAgICAgdmFyaWFudCA9IFwic2lkZWJhclwiLFxuICAgICAgY29sbGFwc2libGUgPSBcIm9mZmNhbnZhc1wiLFxuICAgICAgY2xhc3NOYW1lLFxuICAgICAgY2hpbGRyZW4sXG4gICAgICAuLi5wcm9wc1xuICAgIH0sXG4gICAgcmVmXG4gICkgPT4ge1xuICAgIGNvbnN0IHsgaXNNb2JpbGUsIHN0YXRlLCBvcGVuTW9iaWxlLCBzZXRPcGVuTW9iaWxlIH0gPSB1c2VTaWRlYmFyKClcblxuICAgIGlmIChjb2xsYXBzaWJsZSA9PT0gXCJub25lXCIpIHtcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgXCJmbGV4IGgtZnVsbCB3LVstLXNpZGViYXItd2lkdGhdIGZsZXgtY29sIGJnLXNpZGViYXIgdGV4dC1zaWRlYmFyLWZvcmVncm91bmRcIixcbiAgICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICAgICl9XG4gICAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgICAgey4uLnByb3BzfVxuICAgICAgICA+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIClcbiAgICB9XG5cbiAgICBpZiAoaXNNb2JpbGUpIHtcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxTaGVldCBvcGVuPXtvcGVuTW9iaWxlfSBvbk9wZW5DaGFuZ2U9e3NldE9wZW5Nb2JpbGV9IHsuLi5wcm9wc30+XG4gICAgICAgICAgPFNoZWV0Q29udGVudFxuICAgICAgICAgICAgZGF0YS1zaWRlYmFyPVwic2lkZWJhclwiXG4gICAgICAgICAgICBkYXRhLW1vYmlsZT1cInRydWVcIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1bLS1zaWRlYmFyLXdpZHRoXSBiZy1zaWRlYmFyIHAtMCB0ZXh0LXNpZGViYXItZm9yZWdyb3VuZCBbJj5idXR0b25dOmhpZGRlblwiXG4gICAgICAgICAgICBzdHlsZT17XG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICBcIi0tc2lkZWJhci13aWR0aFwiOiBTSURFQkFSX1dJRFRIX01PQklMRSxcbiAgICAgICAgICAgICAgfSBhcyBSZWFjdC5DU1NQcm9wZXJ0aWVzXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBzaWRlPXtzaWRlfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTaGVldEhlYWRlciBjbGFzc05hbWU9XCJzci1vbmx5XCI+XG4gICAgICAgICAgICAgIDxTaGVldFRpdGxlPlNpZGViYXI8L1NoZWV0VGl0bGU+XG4gICAgICAgICAgICAgIDxTaGVldERlc2NyaXB0aW9uPkRpc3BsYXlzIHRoZSBtb2JpbGUgc2lkZWJhci48L1NoZWV0RGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L1NoZWV0SGVhZGVyPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtZnVsbCB3LWZ1bGwgZmxleC1jb2xcIj57Y2hpbGRyZW59PC9kaXY+XG4gICAgICAgICAgPC9TaGVldENvbnRlbnQ+XG4gICAgICAgIDwvU2hlZXQ+XG4gICAgICApXG4gICAgfVxuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXZcbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIHBlZXIgaGlkZGVuIHRleHQtc2lkZWJhci1mb3JlZ3JvdW5kIG1kOmJsb2NrXCJcbiAgICAgICAgZGF0YS1zdGF0ZT17c3RhdGV9XG4gICAgICAgIGRhdGEtY29sbGFwc2libGU9e3N0YXRlID09PSBcImNvbGxhcHNlZFwiID8gY29sbGFwc2libGUgOiBcIlwifVxuICAgICAgICBkYXRhLXZhcmlhbnQ9e3ZhcmlhbnR9XG4gICAgICAgIGRhdGEtc2lkZT17c2lkZX1cbiAgICAgID5cbiAgICAgICAgey8qIFRoaXMgaXMgd2hhdCBoYW5kbGVzIHRoZSBzaWRlYmFyIGdhcCBvbiBkZXNrdG9wICovfVxuICAgICAgICA8ZGl2XG4gICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgIFwicmVsYXRpdmUgdy1bLS1zaWRlYmFyLXdpZHRoXSBiZy10cmFuc3BhcmVudCB0cmFuc2l0aW9uLVt3aWR0aF0gZHVyYXRpb24tMjAwIGVhc2UtbGluZWFyXCIsXG4gICAgICAgICAgICBcImdyb3VwLWRhdGEtW2NvbGxhcHNpYmxlPW9mZmNhbnZhc106dy0wXCIsXG4gICAgICAgICAgICBcImdyb3VwLWRhdGEtW3NpZGU9cmlnaHRdOnJvdGF0ZS0xODBcIixcbiAgICAgICAgICAgIHZhcmlhbnQgPT09IFwiZmxvYXRpbmdcIiB8fCB2YXJpYW50ID09PSBcImluc2V0XCJcbiAgICAgICAgICAgICAgPyBcImdyb3VwLWRhdGEtW2NvbGxhcHNpYmxlPWljb25dOnctW2NhbGModmFyKC0tc2lkZWJhci13aWR0aC1pY29uKV8rX3RoZW1lKHNwYWNpbmcuNCkpXVwiXG4gICAgICAgICAgICAgIDogXCJncm91cC1kYXRhLVtjb2xsYXBzaWJsZT1pY29uXTp3LVstLXNpZGViYXItd2lkdGgtaWNvbl1cIlxuICAgICAgICAgICl9XG4gICAgICAgIC8+XG4gICAgICAgIDxkaXZcbiAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgXCJmaXhlZCBpbnNldC15LTAgei0xMCBoaWRkZW4gaC1zdmggdy1bLS1zaWRlYmFyLXdpZHRoXSB0cmFuc2l0aW9uLVtsZWZ0LHJpZ2h0LHdpZHRoXSBkdXJhdGlvbi0yMDAgZWFzZS1saW5lYXIgbWQ6ZmxleFwiLFxuICAgICAgICAgICAgc2lkZSA9PT0gXCJsZWZ0XCJcbiAgICAgICAgICAgICAgPyBcImxlZnQtMCBncm91cC1kYXRhLVtjb2xsYXBzaWJsZT1vZmZjYW52YXNdOmxlZnQtW2NhbGModmFyKC0tc2lkZWJhci13aWR0aCkqLTEpXVwiXG4gICAgICAgICAgICAgIDogXCJyaWdodC0wIGdyb3VwLWRhdGEtW2NvbGxhcHNpYmxlPW9mZmNhbnZhc106cmlnaHQtW2NhbGModmFyKC0tc2lkZWJhci13aWR0aCkqLTEpXVwiLFxuICAgICAgICAgICAgLy8gQWRqdXN0IHRoZSBwYWRkaW5nIGZvciBmbG9hdGluZyBhbmQgaW5zZXQgdmFyaWFudHMuXG4gICAgICAgICAgICB2YXJpYW50ID09PSBcImZsb2F0aW5nXCIgfHwgdmFyaWFudCA9PT0gXCJpbnNldFwiXG4gICAgICAgICAgICAgID8gXCJwLTIgZ3JvdXAtZGF0YS1bY29sbGFwc2libGU9aWNvbl06dy1bY2FsYyh2YXIoLS1zaWRlYmFyLXdpZHRoLWljb24pXytfdGhlbWUoc3BhY2luZy40KV8rMnB4KV1cIlxuICAgICAgICAgICAgICA6IFwiZ3JvdXAtZGF0YS1bY29sbGFwc2libGU9aWNvbl06dy1bLS1zaWRlYmFyLXdpZHRoLWljb25dIGdyb3VwLWRhdGEtW3NpZGU9bGVmdF06Ym9yZGVyLXIgZ3JvdXAtZGF0YS1bc2lkZT1yaWdodF06Ym9yZGVyLWxcIixcbiAgICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICAgICl9XG4gICAgICAgICAgey4uLnByb3BzfVxuICAgICAgICA+XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgZGF0YS1zaWRlYmFyPVwic2lkZWJhclwiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGgtZnVsbCB3LWZ1bGwgZmxleC1jb2wgYmctc2lkZWJhciBncm91cC1kYXRhLVt2YXJpYW50PWZsb2F0aW5nXTpyb3VuZGVkLWxnIGdyb3VwLWRhdGEtW3ZhcmlhbnQ9ZmxvYXRpbmddOmJvcmRlciBncm91cC1kYXRhLVt2YXJpYW50PWZsb2F0aW5nXTpib3JkZXItc2lkZWJhci1ib3JkZXIgZ3JvdXAtZGF0YS1bdmFyaWFudD1mbG9hdGluZ106c2hhZG93XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG4pXG5TaWRlYmFyLmRpc3BsYXlOYW1lID0gXCJTaWRlYmFyXCJcblxuY29uc3QgU2lkZWJhclRyaWdnZXIgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBCdXR0b24+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wczx0eXBlb2YgQnV0dG9uPlxuPigoeyBjbGFzc05hbWUsIG9uQ2xpY2ssIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICBjb25zdCB7IHRvZ2dsZVNpZGViYXIgfSA9IHVzZVNpZGViYXIoKVxuXG4gIHJldHVybiAoXG4gICAgPEJ1dHRvblxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBkYXRhLXNpZGViYXI9XCJ0cmlnZ2VyXCJcbiAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICBzaXplPVwiaWNvblwiXG4gICAgICBjbGFzc05hbWU9e2NuKFwiaC03IHctN1wiLCBjbGFzc05hbWUpfVxuICAgICAgb25DbGljaz17KGV2ZW50KSA9PiB7XG4gICAgICAgIG9uQ2xpY2s/LihldmVudClcbiAgICAgICAgdG9nZ2xlU2lkZWJhcigpXG4gICAgICB9fVxuICAgICAgey4uLnByb3BzfVxuICAgID5cbiAgICAgIDxQYW5lbExlZnQgLz5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHlcIj5Ub2dnbGUgU2lkZWJhcjwvc3Bhbj5cbiAgICA8L0J1dHRvbj5cbiAgKVxufSlcblNpZGViYXJUcmlnZ2VyLmRpc3BsYXlOYW1lID0gXCJTaWRlYmFyVHJpZ2dlclwiXG5cbmNvbnN0IFNpZGViYXJSYWlsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTEJ1dHRvbkVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiYnV0dG9uXCI+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gIGNvbnN0IHsgdG9nZ2xlU2lkZWJhciB9ID0gdXNlU2lkZWJhcigpXG5cbiAgcmV0dXJuIChcbiAgICA8YnV0dG9uXG4gICAgICByZWY9e3JlZn1cbiAgICAgIGRhdGEtc2lkZWJhcj1cInJhaWxcIlxuICAgICAgYXJpYS1sYWJlbD1cIlRvZ2dsZSBTaWRlYmFyXCJcbiAgICAgIHRhYkluZGV4PXstMX1cbiAgICAgIG9uQ2xpY2s9e3RvZ2dsZVNpZGViYXJ9XG4gICAgICB0aXRsZT1cIlRvZ2dsZSBTaWRlYmFyXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiYWJzb2x1dGUgaW5zZXQteS0wIHotMjAgaGlkZGVuIHctNCAtdHJhbnNsYXRlLXgtMS8yIHRyYW5zaXRpb24tYWxsIGVhc2UtbGluZWFyIGFmdGVyOmFic29sdXRlIGFmdGVyOmluc2V0LXktMCBhZnRlcjpsZWZ0LTEvMiBhZnRlcjp3LVsycHhdIGhvdmVyOmFmdGVyOmJnLXNpZGViYXItYm9yZGVyIGdyb3VwLWRhdGEtW3NpZGU9bGVmdF06LXJpZ2h0LTQgZ3JvdXAtZGF0YS1bc2lkZT1yaWdodF06bGVmdC0wIHNtOmZsZXhcIixcbiAgICAgICAgXCJbW2RhdGEtc2lkZT1sZWZ0XV8mXTpjdXJzb3Itdy1yZXNpemUgW1tkYXRhLXNpZGU9cmlnaHRdXyZdOmN1cnNvci1lLXJlc2l6ZVwiLFxuICAgICAgICBcIltbZGF0YS1zaWRlPWxlZnRdW2RhdGEtc3RhdGU9Y29sbGFwc2VkXV8mXTpjdXJzb3ItZS1yZXNpemUgW1tkYXRhLXNpZGU9cmlnaHRdW2RhdGEtc3RhdGU9Y29sbGFwc2VkXV8mXTpjdXJzb3Itdy1yZXNpemVcIixcbiAgICAgICAgXCJncm91cC1kYXRhLVtjb2xsYXBzaWJsZT1vZmZjYW52YXNdOnRyYW5zbGF0ZS14LTAgZ3JvdXAtZGF0YS1bY29sbGFwc2libGU9b2ZmY2FudmFzXTphZnRlcjpsZWZ0LWZ1bGwgZ3JvdXAtZGF0YS1bY29sbGFwc2libGU9b2ZmY2FudmFzXTpob3ZlcjpiZy1zaWRlYmFyXCIsXG4gICAgICAgIFwiW1tkYXRhLXNpZGU9bGVmdF1bZGF0YS1jb2xsYXBzaWJsZT1vZmZjYW52YXNdXyZdOi1yaWdodC0yXCIsXG4gICAgICAgIFwiW1tkYXRhLXNpZGU9cmlnaHRdW2RhdGEtY29sbGFwc2libGU9b2ZmY2FudmFzXV8mXTotbGVmdC0yXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59KVxuU2lkZWJhclJhaWwuZGlzcGxheU5hbWUgPSBcIlNpZGViYXJSYWlsXCJcblxuY29uc3QgU2lkZWJhckluc2V0ID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwibWFpblwiPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICByZXR1cm4gKFxuICAgIDxtYWluXG4gICAgICByZWY9e3JlZn1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwicmVsYXRpdmUgZmxleCB3LWZ1bGwgZmxleC0xIGZsZXgtY29sIGJnLWJhY2tncm91bmRcIixcbiAgICAgICAgXCJtZDpwZWVyLWRhdGEtW3ZhcmlhbnQ9aW5zZXRdOm0tMiBtZDpwZWVyLWRhdGEtW3N0YXRlPWNvbGxhcHNlZF06cGVlci1kYXRhLVt2YXJpYW50PWluc2V0XTptbC0yIG1kOnBlZXItZGF0YS1bdmFyaWFudD1pbnNldF06bWwtMCBtZDpwZWVyLWRhdGEtW3ZhcmlhbnQ9aW5zZXRdOnJvdW5kZWQteGwgbWQ6cGVlci1kYXRhLVt2YXJpYW50PWluc2V0XTpzaGFkb3dcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn0pXG5TaWRlYmFySW5zZXQuZGlzcGxheU5hbWUgPSBcIlNpZGViYXJJbnNldFwiXG5cbmNvbnN0IFNpZGViYXJJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIElucHV0PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIElucHV0PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICByZXR1cm4gKFxuICAgIDxJbnB1dFxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBkYXRhLXNpZGViYXI9XCJpbnB1dFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImgtOCB3LWZ1bGwgYmctYmFja2dyb3VuZCBzaGFkb3ctbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctc2lkZWJhci1yaW5nXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59KVxuU2lkZWJhcklucHV0LmRpc3BsYXlOYW1lID0gXCJTaWRlYmFySW5wdXRcIlxuXG5jb25zdCBTaWRlYmFySGVhZGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiZGl2XCI+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBkYXRhLXNpZGViYXI9XCJoZWFkZXJcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcImZsZXggZmxleC1jb2wgZ2FwLTIgcC0yXCIsIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufSlcblNpZGViYXJIZWFkZXIuZGlzcGxheU5hbWUgPSBcIlNpZGViYXJIZWFkZXJcIlxuXG5jb25zdCBTaWRlYmFyRm9vdGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiZGl2XCI+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBkYXRhLXNpZGViYXI9XCJmb290ZXJcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcImZsZXggZmxleC1jb2wgZ2FwLTIgcC0yXCIsIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufSlcblNpZGViYXJGb290ZXIuZGlzcGxheU5hbWUgPSBcIlNpZGViYXJGb290ZXJcIlxuXG5jb25zdCBTaWRlYmFyU2VwYXJhdG9yID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgU2VwYXJhdG9yPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIFNlcGFyYXRvcj5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgcmV0dXJuIChcbiAgICA8U2VwYXJhdG9yXG4gICAgICByZWY9e3JlZn1cbiAgICAgIGRhdGEtc2lkZWJhcj1cInNlcGFyYXRvclwiXG4gICAgICBjbGFzc05hbWU9e2NuKFwibXgtMiB3LWF1dG8gYmctc2lkZWJhci1ib3JkZXJcIiwgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59KVxuU2lkZWJhclNlcGFyYXRvci5kaXNwbGF5TmFtZSA9IFwiU2lkZWJhclNlcGFyYXRvclwiXG5cbmNvbnN0IFNpZGViYXJDb250ZW50ID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiZGl2XCI+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBkYXRhLXNpZGViYXI9XCJjb250ZW50XCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiZmxleCBtaW4taC0wIGZsZXgtMSBmbGV4LWNvbCBnYXAtMiBvdmVyZmxvdy1hdXRvIGdyb3VwLWRhdGEtW2NvbGxhcHNpYmxlPWljb25dOm92ZXJmbG93LWhpZGRlblwiLFxuICAgICAgICBjbGFzc05hbWVcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufSlcblNpZGViYXJDb250ZW50LmRpc3BsYXlOYW1lID0gXCJTaWRlYmFyQ29udGVudFwiXG5cbmNvbnN0IFNpZGViYXJHcm91cCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIHJlZj17cmVmfVxuICAgICAgZGF0YS1zaWRlYmFyPVwiZ3JvdXBcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcInJlbGF0aXZlIGZsZXggdy1mdWxsIG1pbi13LTAgZmxleC1jb2wgcC0yXCIsIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufSlcblNpZGViYXJHcm91cC5kaXNwbGF5TmFtZSA9IFwiU2lkZWJhckdyb3VwXCJcblxuY29uc3QgU2lkZWJhckdyb3VwTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJkaXZcIj4gJiB7IGFzQ2hpbGQ/OiBib29sZWFuIH1cbj4oKHsgY2xhc3NOYW1lLCBhc0NoaWxkID0gZmFsc2UsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBcImRpdlwiXG5cbiAgcmV0dXJuIChcbiAgICA8Q29tcFxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBkYXRhLXNpZGViYXI9XCJncm91cC1sYWJlbFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZsZXggaC04IHNocmluay0wIGl0ZW1zLWNlbnRlciByb3VuZGVkLW1kIHB4LTIgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXNpZGViYXItZm9yZWdyb3VuZC83MCBvdXRsaW5lLW5vbmUgcmluZy1zaWRlYmFyLXJpbmcgdHJhbnNpdGlvbi1bbWFyZ2luLG9wYWNpdHldIGR1cmF0aW9uLTIwMCBlYXNlLWxpbmVhciBmb2N1cy12aXNpYmxlOnJpbmctMiBbJj5zdmddOnNpemUtNCBbJj5zdmddOnNocmluay0wXCIsXG4gICAgICAgIFwiZ3JvdXAtZGF0YS1bY29sbGFwc2libGU9aWNvbl06LW10LTggZ3JvdXAtZGF0YS1bY29sbGFwc2libGU9aWNvbl06b3BhY2l0eS0wXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59KVxuU2lkZWJhckdyb3VwTGFiZWwuZGlzcGxheU5hbWUgPSBcIlNpZGViYXJHcm91cExhYmVsXCJcblxuY29uc3QgU2lkZWJhckdyb3VwQWN0aW9uID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTEJ1dHRvbkVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiYnV0dG9uXCI+ICYgeyBhc0NoaWxkPzogYm9vbGVhbiB9XG4+KCh7IGNsYXNzTmFtZSwgYXNDaGlsZCA9IGZhbHNlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgY29uc3QgQ29tcCA9IGFzQ2hpbGQgPyBTbG90IDogXCJidXR0b25cIlxuXG4gIHJldHVybiAoXG4gICAgPENvbXBcbiAgICAgIHJlZj17cmVmfVxuICAgICAgZGF0YS1zaWRlYmFyPVwiZ3JvdXAtYWN0aW9uXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMy41IGZsZXggYXNwZWN0LXNxdWFyZSB3LTUgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtbWQgcC0wIHRleHQtc2lkZWJhci1mb3JlZ3JvdW5kIG91dGxpbmUtbm9uZSByaW5nLXNpZGViYXItcmluZyB0cmFuc2l0aW9uLXRyYW5zZm9ybSBob3ZlcjpiZy1zaWRlYmFyLWFjY2VudCBob3Zlcjp0ZXh0LXNpZGViYXItYWNjZW50LWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpyaW5nLTIgWyY+c3ZnXTpzaXplLTQgWyY+c3ZnXTpzaHJpbmstMFwiLFxuICAgICAgICAvLyBJbmNyZWFzZXMgdGhlIGhpdCBhcmVhIG9mIHRoZSBidXR0b24gb24gbW9iaWxlLlxuICAgICAgICBcImFmdGVyOmFic29sdXRlIGFmdGVyOi1pbnNldC0yIGFmdGVyOm1kOmhpZGRlblwiLFxuICAgICAgICBcImdyb3VwLWRhdGEtW2NvbGxhcHNpYmxlPWljb25dOmhpZGRlblwiLFxuICAgICAgICBjbGFzc05hbWVcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufSlcblNpZGViYXJHcm91cEFjdGlvbi5kaXNwbGF5TmFtZSA9IFwiU2lkZWJhckdyb3VwQWN0aW9uXCJcblxuY29uc3QgU2lkZWJhckdyb3VwQ29udGVudCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgZGF0YS1zaWRlYmFyPVwiZ3JvdXAtY29udGVudFwiXG4gICAgY2xhc3NOYW1lPXtjbihcInctZnVsbCB0ZXh0LXNtXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcblNpZGViYXJHcm91cENvbnRlbnQuZGlzcGxheU5hbWUgPSBcIlNpZGViYXJHcm91cENvbnRlbnRcIlxuXG5jb25zdCBTaWRlYmFyTWVudSA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxVTGlzdEVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwidWxcIj5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPHVsXG4gICAgcmVmPXtyZWZ9XG4gICAgZGF0YS1zaWRlYmFyPVwibWVudVwiXG4gICAgY2xhc3NOYW1lPXtjbihcImZsZXggdy1mdWxsIG1pbi13LTAgZmxleC1jb2wgZ2FwLTFcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuU2lkZWJhck1lbnUuZGlzcGxheU5hbWUgPSBcIlNpZGViYXJNZW51XCJcblxuY29uc3QgU2lkZWJhck1lbnVJdGVtID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTExJRWxlbWVudCxcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJsaVwiPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8bGlcbiAgICByZWY9e3JlZn1cbiAgICBkYXRhLXNpZGViYXI9XCJtZW51LWl0ZW1cIlxuICAgIGNsYXNzTmFtZT17Y24oXCJncm91cC9tZW51LWl0ZW0gcmVsYXRpdmVcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuU2lkZWJhck1lbnVJdGVtLmRpc3BsYXlOYW1lID0gXCJTaWRlYmFyTWVudUl0ZW1cIlxuXG5jb25zdCBzaWRlYmFyTWVudUJ1dHRvblZhcmlhbnRzID0gY3ZhKFxuICBcInBlZXIvbWVudS1idXR0b24gZmxleCB3LWZ1bGwgaXRlbXMtY2VudGVyIGdhcC0yIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLW1kIHAtMiB0ZXh0LWxlZnQgdGV4dC1zbSBvdXRsaW5lLW5vbmUgcmluZy1zaWRlYmFyLXJpbmcgdHJhbnNpdGlvbi1bd2lkdGgsaGVpZ2h0LHBhZGRpbmddIGhvdmVyOmJnLXNpZGViYXItYWNjZW50IGhvdmVyOnRleHQtc2lkZWJhci1hY2NlbnQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOnJpbmctMiBhY3RpdmU6Ymctc2lkZWJhci1hY2NlbnQgYWN0aXZlOnRleHQtc2lkZWJhci1hY2NlbnQtZm9yZWdyb3VuZCBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgZ3JvdXAtaGFzLVtbZGF0YS1zaWRlYmFyPW1lbnUtYWN0aW9uXV0vbWVudS1pdGVtOnByLTggYXJpYS1kaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGFyaWEtZGlzYWJsZWQ6b3BhY2l0eS01MCBkYXRhLVthY3RpdmU9dHJ1ZV06Ymctc2lkZWJhci1hY2NlbnQgZGF0YS1bYWN0aXZlPXRydWVdOmZvbnQtbWVkaXVtIGRhdGEtW2FjdGl2ZT10cnVlXTp0ZXh0LXNpZGViYXItYWNjZW50LWZvcmVncm91bmQgZGF0YS1bc3RhdGU9b3Blbl06aG92ZXI6Ymctc2lkZWJhci1hY2NlbnQgZGF0YS1bc3RhdGU9b3Blbl06aG92ZXI6dGV4dC1zaWRlYmFyLWFjY2VudC1mb3JlZ3JvdW5kIGdyb3VwLWRhdGEtW2NvbGxhcHNpYmxlPWljb25dOiFzaXplLTggZ3JvdXAtZGF0YS1bY29sbGFwc2libGU9aWNvbl06IXAtMiBbJj5zcGFuOmxhc3QtY2hpbGRdOnRydW5jYXRlIFsmPnN2Z106c2l6ZS00IFsmPnN2Z106c2hyaW5rLTBcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiaG92ZXI6Ymctc2lkZWJhci1hY2NlbnQgaG92ZXI6dGV4dC1zaWRlYmFyLWFjY2VudC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgIG91dGxpbmU6XG4gICAgICAgICAgXCJiZy1iYWNrZ3JvdW5kIHNoYWRvdy1bMF8wXzBfMXB4X2hzbCh2YXIoLS1zaWRlYmFyLWJvcmRlcikpXSBob3ZlcjpiZy1zaWRlYmFyLWFjY2VudCBob3Zlcjp0ZXh0LXNpZGViYXItYWNjZW50LWZvcmVncm91bmQgaG92ZXI6c2hhZG93LVswXzBfMF8xcHhfaHNsKHZhcigtLXNpZGViYXItYWNjZW50KSldXCIsXG4gICAgICB9LFxuICAgICAgc2l6ZToge1xuICAgICAgICBkZWZhdWx0OiBcImgtOCB0ZXh0LXNtXCIsXG4gICAgICAgIHNtOiBcImgtNyB0ZXh0LXhzXCIsXG4gICAgICAgIGxnOiBcImgtMTIgdGV4dC1zbSBncm91cC1kYXRhLVtjb2xsYXBzaWJsZT1pY29uXTohcC0wXCIsXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcbiAgICAgIHNpemU6IFwiZGVmYXVsdFwiLFxuICAgIH0sXG4gIH1cbilcblxuY29uc3QgU2lkZWJhck1lbnVCdXR0b24gPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MQnV0dG9uRWxlbWVudCxcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJidXR0b25cIj4gJiB7XG4gICAgYXNDaGlsZD86IGJvb2xlYW5cbiAgICBpc0FjdGl2ZT86IGJvb2xlYW5cbiAgICB0b29sdGlwPzogc3RyaW5nIHwgUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIFRvb2x0aXBDb250ZW50PlxuICB9ICYgVmFyaWFudFByb3BzPHR5cGVvZiBzaWRlYmFyTWVudUJ1dHRvblZhcmlhbnRzPlxuPihcbiAgKFxuICAgIHtcbiAgICAgIGFzQ2hpbGQgPSBmYWxzZSxcbiAgICAgIGlzQWN0aXZlID0gZmFsc2UsXG4gICAgICB2YXJpYW50ID0gXCJkZWZhdWx0XCIsXG4gICAgICBzaXplID0gXCJkZWZhdWx0XCIsXG4gICAgICB0b29sdGlwLFxuICAgICAgY2xhc3NOYW1lLFxuICAgICAgLi4ucHJvcHNcbiAgICB9LFxuICAgIHJlZlxuICApID0+IHtcbiAgICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBcImJ1dHRvblwiXG4gICAgY29uc3QgeyBpc01vYmlsZSwgc3RhdGUgfSA9IHVzZVNpZGViYXIoKVxuXG4gICAgY29uc3QgYnV0dG9uID0gKFxuICAgICAgPENvbXBcbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIGRhdGEtc2lkZWJhcj1cIm1lbnUtYnV0dG9uXCJcbiAgICAgICAgZGF0YS1zaXplPXtzaXplfVxuICAgICAgICBkYXRhLWFjdGl2ZT17aXNBY3RpdmV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oc2lkZWJhck1lbnVCdXR0b25WYXJpYW50cyh7IHZhcmlhbnQsIHNpemUgfSksIGNsYXNzTmFtZSl9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuXG4gICAgaWYgKCF0b29sdGlwKSB7XG4gICAgICByZXR1cm4gYnV0dG9uXG4gICAgfVxuXG4gICAgaWYgKHR5cGVvZiB0b29sdGlwID09PSBcInN0cmluZ1wiKSB7XG4gICAgICB0b29sdGlwID0ge1xuICAgICAgICBjaGlsZHJlbjogdG9vbHRpcCxcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgPFRvb2x0aXA+XG4gICAgICAgIDxUb29sdGlwVHJpZ2dlciBhc0NoaWxkPntidXR0b259PC9Ub29sdGlwVHJpZ2dlcj5cbiAgICAgICAgPFRvb2x0aXBDb250ZW50XG4gICAgICAgICAgc2lkZT1cInJpZ2h0XCJcbiAgICAgICAgICBhbGlnbj1cImNlbnRlclwiXG4gICAgICAgICAgaGlkZGVuPXtzdGF0ZSAhPT0gXCJjb2xsYXBzZWRcIiB8fCBpc01vYmlsZX1cbiAgICAgICAgICB7Li4udG9vbHRpcH1cbiAgICAgICAgLz5cbiAgICAgIDwvVG9vbHRpcD5cbiAgICApXG4gIH1cbilcblNpZGViYXJNZW51QnV0dG9uLmRpc3BsYXlOYW1lID0gXCJTaWRlYmFyTWVudUJ1dHRvblwiXG5cbmNvbnN0IFNpZGViYXJNZW51QWN0aW9uID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTEJ1dHRvbkVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiYnV0dG9uXCI+ICYge1xuICAgIGFzQ2hpbGQ/OiBib29sZWFuXG4gICAgc2hvd09uSG92ZXI/OiBib29sZWFuXG4gIH1cbj4oKHsgY2xhc3NOYW1lLCBhc0NoaWxkID0gZmFsc2UsIHNob3dPbkhvdmVyID0gZmFsc2UsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBcImJ1dHRvblwiXG5cbiAgcmV0dXJuIChcbiAgICA8Q29tcFxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBkYXRhLXNpZGViYXI9XCJtZW51LWFjdGlvblwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImFic29sdXRlIHJpZ2h0LTEgdG9wLTEuNSBmbGV4IGFzcGVjdC1zcXVhcmUgdy01IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLW1kIHAtMCB0ZXh0LXNpZGViYXItZm9yZWdyb3VuZCBvdXRsaW5lLW5vbmUgcmluZy1zaWRlYmFyLXJpbmcgdHJhbnNpdGlvbi10cmFuc2Zvcm0gaG92ZXI6Ymctc2lkZWJhci1hY2NlbnQgaG92ZXI6dGV4dC1zaWRlYmFyLWFjY2VudC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6cmluZy0yIHBlZXItaG92ZXIvbWVudS1idXR0b246dGV4dC1zaWRlYmFyLWFjY2VudC1mb3JlZ3JvdW5kIFsmPnN2Z106c2l6ZS00IFsmPnN2Z106c2hyaW5rLTBcIixcbiAgICAgICAgLy8gSW5jcmVhc2VzIHRoZSBoaXQgYXJlYSBvZiB0aGUgYnV0dG9uIG9uIG1vYmlsZS5cbiAgICAgICAgXCJhZnRlcjphYnNvbHV0ZSBhZnRlcjotaW5zZXQtMiBhZnRlcjptZDpoaWRkZW5cIixcbiAgICAgICAgXCJwZWVyLWRhdGEtW3NpemU9c21dL21lbnUtYnV0dG9uOnRvcC0xXCIsXG4gICAgICAgIFwicGVlci1kYXRhLVtzaXplPWRlZmF1bHRdL21lbnUtYnV0dG9uOnRvcC0xLjVcIixcbiAgICAgICAgXCJwZWVyLWRhdGEtW3NpemU9bGddL21lbnUtYnV0dG9uOnRvcC0yLjVcIixcbiAgICAgICAgXCJncm91cC1kYXRhLVtjb2xsYXBzaWJsZT1pY29uXTpoaWRkZW5cIixcbiAgICAgICAgc2hvd09uSG92ZXIgJiZcbiAgICAgICAgICBcImdyb3VwLWZvY3VzLXdpdGhpbi9tZW51LWl0ZW06b3BhY2l0eS0xMDAgZ3JvdXAtaG92ZXIvbWVudS1pdGVtOm9wYWNpdHktMTAwIGRhdGEtW3N0YXRlPW9wZW5dOm9wYWNpdHktMTAwIHBlZXItZGF0YS1bYWN0aXZlPXRydWVdL21lbnUtYnV0dG9uOnRleHQtc2lkZWJhci1hY2NlbnQtZm9yZWdyb3VuZCBtZDpvcGFjaXR5LTBcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn0pXG5TaWRlYmFyTWVudUFjdGlvbi5kaXNwbGF5TmFtZSA9IFwiU2lkZWJhck1lbnVBY3Rpb25cIlxuXG5jb25zdCBTaWRlYmFyTWVudUJhZGdlID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiZGl2XCI+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBkYXRhLXNpZGViYXI9XCJtZW51LWJhZGdlXCJcbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJwb2ludGVyLWV2ZW50cy1ub25lIGFic29sdXRlIHJpZ2h0LTEgZmxleCBoLTUgbWluLXctNSBzZWxlY3Qtbm9uZSBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1tZCBweC0xIHRleHQteHMgZm9udC1tZWRpdW0gdGFidWxhci1udW1zIHRleHQtc2lkZWJhci1mb3JlZ3JvdW5kXCIsXG4gICAgICBcInBlZXItaG92ZXIvbWVudS1idXR0b246dGV4dC1zaWRlYmFyLWFjY2VudC1mb3JlZ3JvdW5kIHBlZXItZGF0YS1bYWN0aXZlPXRydWVdL21lbnUtYnV0dG9uOnRleHQtc2lkZWJhci1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgXCJwZWVyLWRhdGEtW3NpemU9c21dL21lbnUtYnV0dG9uOnRvcC0xXCIsXG4gICAgICBcInBlZXItZGF0YS1bc2l6ZT1kZWZhdWx0XS9tZW51LWJ1dHRvbjp0b3AtMS41XCIsXG4gICAgICBcInBlZXItZGF0YS1bc2l6ZT1sZ10vbWVudS1idXR0b246dG9wLTIuNVwiLFxuICAgICAgXCJncm91cC1kYXRhLVtjb2xsYXBzaWJsZT1pY29uXTpoaWRkZW5cIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcblNpZGViYXJNZW51QmFkZ2UuZGlzcGxheU5hbWUgPSBcIlNpZGViYXJNZW51QmFkZ2VcIlxuXG5jb25zdCBTaWRlYmFyTWVudVNrZWxldG9uID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiZGl2XCI+ICYge1xuICAgIHNob3dJY29uPzogYm9vbGVhblxuICB9XG4+KCh7IGNsYXNzTmFtZSwgc2hvd0ljb24gPSBmYWxzZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gIC8vIFJhbmRvbSB3aWR0aCBiZXR3ZWVuIDUwIHRvIDkwJS5cbiAgY29uc3Qgd2lkdGggPSBSZWFjdC51c2VNZW1vKCgpID0+IHtcbiAgICByZXR1cm4gYCR7TWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogNDApICsgNTB9JWBcbiAgfSwgW10pXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICByZWY9e3JlZn1cbiAgICAgIGRhdGEtc2lkZWJhcj1cIm1lbnUtc2tlbGV0b25cIlxuICAgICAgY2xhc3NOYW1lPXtjbihcImZsZXggaC04IGl0ZW1zLWNlbnRlciBnYXAtMiByb3VuZGVkLW1kIHB4LTJcIiwgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICA+XG4gICAgICB7c2hvd0ljb24gJiYgKFxuICAgICAgICA8U2tlbGV0b25cbiAgICAgICAgICBjbGFzc05hbWU9XCJzaXplLTQgcm91bmRlZC1tZFwiXG4gICAgICAgICAgZGF0YS1zaWRlYmFyPVwibWVudS1za2VsZXRvbi1pY29uXCJcbiAgICAgICAgLz5cbiAgICAgICl9XG4gICAgICA8U2tlbGV0b25cbiAgICAgICAgY2xhc3NOYW1lPVwiaC00IG1heC13LVstLXNrZWxldG9uLXdpZHRoXSBmbGV4LTFcIlxuICAgICAgICBkYXRhLXNpZGViYXI9XCJtZW51LXNrZWxldG9uLXRleHRcIlxuICAgICAgICBzdHlsZT17XG4gICAgICAgICAge1xuICAgICAgICAgICAgXCItLXNrZWxldG9uLXdpZHRoXCI6IHdpZHRoLFxuICAgICAgICAgIH0gYXMgUmVhY3QuQ1NTUHJvcGVydGllc1xuICAgICAgICB9XG4gICAgICAvPlxuICAgIDwvZGl2PlxuICApXG59KVxuU2lkZWJhck1lbnVTa2VsZXRvbi5kaXNwbGF5TmFtZSA9IFwiU2lkZWJhck1lbnVTa2VsZXRvblwiXG5cbmNvbnN0IFNpZGViYXJNZW51U3ViID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTFVMaXN0RWxlbWVudCxcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJ1bFwiPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8dWxcbiAgICByZWY9e3JlZn1cbiAgICBkYXRhLXNpZGViYXI9XCJtZW51LXN1YlwiXG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwibXgtMy41IGZsZXggbWluLXctMCB0cmFuc2xhdGUteC1weCBmbGV4LWNvbCBnYXAtMSBib3JkZXItbCBib3JkZXItc2lkZWJhci1ib3JkZXIgcHgtMi41IHB5LTAuNVwiLFxuICAgICAgXCJncm91cC1kYXRhLVtjb2xsYXBzaWJsZT1pY29uXTpoaWRkZW5cIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcblNpZGViYXJNZW51U3ViLmRpc3BsYXlOYW1lID0gXCJTaWRlYmFyTWVudVN1YlwiXG5cbmNvbnN0IFNpZGViYXJNZW51U3ViSXRlbSA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxMSUVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwibGlcIj5cbj4oKHsgLi4ucHJvcHMgfSwgcmVmKSA9PiA8bGkgcmVmPXtyZWZ9IHsuLi5wcm9wc30gLz4pXG5TaWRlYmFyTWVudVN1Ykl0ZW0uZGlzcGxheU5hbWUgPSBcIlNpZGViYXJNZW51U3ViSXRlbVwiXG5cbmNvbnN0IFNpZGViYXJNZW51U3ViQnV0dG9uID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTEFuY2hvckVsZW1lbnQsXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiYVwiPiAmIHtcbiAgICBhc0NoaWxkPzogYm9vbGVhblxuICAgIHNpemU/OiBcInNtXCIgfCBcIm1kXCJcbiAgICBpc0FjdGl2ZT86IGJvb2xlYW5cbiAgfVxuPigoeyBhc0NoaWxkID0gZmFsc2UsIHNpemUgPSBcIm1kXCIsIGlzQWN0aXZlLCBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBcImFcIlxuXG4gIHJldHVybiAoXG4gICAgPENvbXBcbiAgICAgIHJlZj17cmVmfVxuICAgICAgZGF0YS1zaWRlYmFyPVwibWVudS1zdWItYnV0dG9uXCJcbiAgICAgIGRhdGEtc2l6ZT17c2l6ZX1cbiAgICAgIGRhdGEtYWN0aXZlPXtpc0FjdGl2ZX1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiZmxleCBoLTcgbWluLXctMCAtdHJhbnNsYXRlLXgtcHggaXRlbXMtY2VudGVyIGdhcC0yIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLW1kIHB4LTIgdGV4dC1zaWRlYmFyLWZvcmVncm91bmQgb3V0bGluZS1ub25lIHJpbmctc2lkZWJhci1yaW5nIGhvdmVyOmJnLXNpZGViYXItYWNjZW50IGhvdmVyOnRleHQtc2lkZWJhci1hY2NlbnQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOnJpbmctMiBhY3RpdmU6Ymctc2lkZWJhci1hY2NlbnQgYWN0aXZlOnRleHQtc2lkZWJhci1hY2NlbnQtZm9yZWdyb3VuZCBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgYXJpYS1kaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGFyaWEtZGlzYWJsZWQ6b3BhY2l0eS01MCBbJj5zcGFuOmxhc3QtY2hpbGRdOnRydW5jYXRlIFsmPnN2Z106c2l6ZS00IFsmPnN2Z106c2hyaW5rLTAgWyY+c3ZnXTp0ZXh0LXNpZGViYXItYWNjZW50LWZvcmVncm91bmRcIixcbiAgICAgICAgXCJkYXRhLVthY3RpdmU9dHJ1ZV06Ymctc2lkZWJhci1hY2NlbnQgZGF0YS1bYWN0aXZlPXRydWVdOnRleHQtc2lkZWJhci1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBzaXplID09PSBcInNtXCIgJiYgXCJ0ZXh0LXhzXCIsXG4gICAgICAgIHNpemUgPT09IFwibWRcIiAmJiBcInRleHQtc21cIixcbiAgICAgICAgXCJncm91cC1kYXRhLVtjb2xsYXBzaWJsZT1pY29uXTpoaWRkZW5cIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn0pXG5TaWRlYmFyTWVudVN1YkJ1dHRvbi5kaXNwbGF5TmFtZSA9IFwiU2lkZWJhck1lbnVTdWJCdXR0b25cIlxuXG5leHBvcnQge1xuICBTaWRlYmFyLFxuICBTaWRlYmFyQ29udGVudCxcbiAgU2lkZWJhckZvb3RlcixcbiAgU2lkZWJhckdyb3VwLFxuICBTaWRlYmFyR3JvdXBBY3Rpb24sXG4gIFNpZGViYXJHcm91cENvbnRlbnQsXG4gIFNpZGViYXJHcm91cExhYmVsLFxuICBTaWRlYmFySGVhZGVyLFxuICBTaWRlYmFySW5wdXQsXG4gIFNpZGViYXJJbnNldCxcbiAgU2lkZWJhck1lbnUsXG4gIFNpZGViYXJNZW51QWN0aW9uLFxuICBTaWRlYmFyTWVudUJhZGdlLFxuICBTaWRlYmFyTWVudUJ1dHRvbixcbiAgU2lkZWJhck1lbnVJdGVtLFxuICBTaWRlYmFyTWVudVNrZWxldG9uLFxuICBTaWRlYmFyTWVudVN1YixcbiAgU2lkZWJhck1lbnVTdWJCdXR0b24sXG4gIFNpZGViYXJNZW51U3ViSXRlbSxcbiAgU2lkZWJhclByb3ZpZGVyLFxuICBTaWRlYmFyUmFpbCxcbiAgU2lkZWJhclNlcGFyYXRvcixcbiAgU2lkZWJhclRyaWdnZXIsXG4gIHVzZVNpZGViYXIsXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTbG90IiwiY3ZhIiwiUGFuZWxMZWZ0IiwidXNlSXNNb2JpbGUiLCJjbiIsIkJ1dHRvbiIsIklucHV0IiwiU2VwYXJhdG9yIiwiU2hlZXQiLCJTaGVldENvbnRlbnQiLCJTaGVldERlc2NyaXB0aW9uIiwiU2hlZXRIZWFkZXIiLCJTaGVldFRpdGxlIiwiU2tlbGV0b24iLCJUb29sdGlwIiwiVG9vbHRpcENvbnRlbnQiLCJUb29sdGlwUHJvdmlkZXIiLCJUb29sdGlwVHJpZ2dlciIsIlNJREVCQVJfQ09PS0lFX05BTUUiLCJTSURFQkFSX0NPT0tJRV9NQVhfQUdFIiwiU0lERUJBUl9XSURUSCIsIlNJREVCQVJfV0lEVEhfTU9CSUxFIiwiU0lERUJBUl9XSURUSF9JQ09OIiwiU0lERUJBUl9LRVlCT0FSRF9TSE9SVENVVCIsIlNpZGViYXJDb250ZXh0IiwiY3JlYXRlQ29udGV4dCIsInVzZVNpZGViYXIiLCJjb250ZXh0IiwidXNlQ29udGV4dCIsIkVycm9yIiwiU2lkZWJhclByb3ZpZGVyIiwiZm9yd2FyZFJlZiIsImRlZmF1bHRPcGVuIiwib3BlbiIsIm9wZW5Qcm9wIiwib25PcGVuQ2hhbmdlIiwic2V0T3BlblByb3AiLCJjbGFzc05hbWUiLCJzdHlsZSIsImNoaWxkcmVuIiwicHJvcHMiLCJyZWYiLCJpc01vYmlsZSIsIm9wZW5Nb2JpbGUiLCJzZXRPcGVuTW9iaWxlIiwidXNlU3RhdGUiLCJfb3BlbiIsIl9zZXRPcGVuIiwic2V0T3BlbiIsInVzZUNhbGxiYWNrIiwidmFsdWUiLCJvcGVuU3RhdGUiLCJkb2N1bWVudCIsImNvb2tpZSIsInRvZ2dsZVNpZGViYXIiLCJ1c2VFZmZlY3QiLCJoYW5kbGVLZXlEb3duIiwiZXZlbnQiLCJrZXkiLCJtZXRhS2V5IiwiY3RybEtleSIsInByZXZlbnREZWZhdWx0Iiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJzdGF0ZSIsImNvbnRleHRWYWx1ZSIsInVzZU1lbW8iLCJQcm92aWRlciIsImRlbGF5RHVyYXRpb24iLCJkaXYiLCJkaXNwbGF5TmFtZSIsIlNpZGViYXIiLCJzaWRlIiwidmFyaWFudCIsImNvbGxhcHNpYmxlIiwiZGF0YS1zaWRlYmFyIiwiZGF0YS1tb2JpbGUiLCJkYXRhLXN0YXRlIiwiZGF0YS1jb2xsYXBzaWJsZSIsImRhdGEtdmFyaWFudCIsImRhdGEtc2lkZSIsIlNpZGViYXJUcmlnZ2VyIiwib25DbGljayIsInNpemUiLCJzcGFuIiwiU2lkZWJhclJhaWwiLCJidXR0b24iLCJhcmlhLWxhYmVsIiwidGFiSW5kZXgiLCJ0aXRsZSIsIlNpZGViYXJJbnNldCIsIm1haW4iLCJTaWRlYmFySW5wdXQiLCJTaWRlYmFySGVhZGVyIiwiU2lkZWJhckZvb3RlciIsIlNpZGViYXJTZXBhcmF0b3IiLCJTaWRlYmFyQ29udGVudCIsIlNpZGViYXJHcm91cCIsIlNpZGViYXJHcm91cExhYmVsIiwiYXNDaGlsZCIsIkNvbXAiLCJTaWRlYmFyR3JvdXBBY3Rpb24iLCJTaWRlYmFyR3JvdXBDb250ZW50IiwiU2lkZWJhck1lbnUiLCJ1bCIsIlNpZGViYXJNZW51SXRlbSIsImxpIiwic2lkZWJhck1lbnVCdXR0b25WYXJpYW50cyIsInZhcmlhbnRzIiwiZGVmYXVsdCIsIm91dGxpbmUiLCJzbSIsImxnIiwiZGVmYXVsdFZhcmlhbnRzIiwiU2lkZWJhck1lbnVCdXR0b24iLCJpc0FjdGl2ZSIsInRvb2x0aXAiLCJkYXRhLXNpemUiLCJkYXRhLWFjdGl2ZSIsImFsaWduIiwiaGlkZGVuIiwiU2lkZWJhck1lbnVBY3Rpb24iLCJzaG93T25Ib3ZlciIsIlNpZGViYXJNZW51QmFkZ2UiLCJTaWRlYmFyTWVudVNrZWxldG9uIiwic2hvd0ljb24iLCJ3aWR0aCIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsIlNpZGViYXJNZW51U3ViIiwiU2lkZWJhck1lbnVTdWJJdGVtIiwiU2lkZWJhck1lbnVTdWJCdXR0b24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/skeleton.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/skeleton.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\nfunction Skeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9za2VsZXRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFFaEMsU0FBU0MsU0FBUyxFQUNoQkMsU0FBUyxFQUNULEdBQUdDLE9BQ2tDO0lBQ3JDLHFCQUNFLDhEQUFDQztRQUNDRixXQUFXRiw4Q0FBRUEsQ0FBQyxxQ0FBcUNFO1FBQ2xELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBRW1CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxGTFxcVmVsb2NpdHlcXHN0dWR5YnVkZHktZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcdWlcXHNrZWxldG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmZ1bmN0aW9uIFNrZWxldG9uKHtcbiAgY2xhc3NOYW1lLFxuICAuLi5wcm9wc1xufTogUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPXtjbihcImFuaW1hdGUtcHVsc2Ugcm91bmRlZC1tZCBiZy1tdXRlZFwiLCBjbGFzc05hbWUpfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn1cblxuZXhwb3J0IHsgU2tlbGV0b24gfVxuIl0sIm5hbWVzIjpbImNuIiwiU2tlbGV0b24iLCJjbGFzc05hbWUiLCJwcm9wcyIsImRpdiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/skeleton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/tooltip.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/tooltip.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tooltip */ \"(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tooltip,TooltipTrigger,TooltipContent,TooltipProvider auto */ \n\n\n\nconst TooltipProvider = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Provider;\nconst Tooltip = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst TooltipTrigger = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        ref: ref,\n        sideOffset: sideOffset,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nTooltipContent.displayName = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/tooltip.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-mobile.tsx":
/*!**********************************!*\
  !*** ./src/hooks/use-mobile.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsMobile: () => (/* binding */ useIsMobile)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MOBILE_BREAKPOINT = 768;\nfunction useIsMobile() {\n    const [isMobile, setIsMobile] = react__WEBPACK_IMPORTED_MODULE_0__.useState(undefined);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useIsMobile.useEffect\": ()=>{\n            const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\n            const onChange = {\n                \"useIsMobile.useEffect.onChange\": ()=>{\n                    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n                }\n            }[\"useIsMobile.useEffect.onChange\"];\n            mql.addEventListener(\"change\", onChange);\n            setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n            return ({\n                \"useIsMobile.useEffect\": ()=>mql.removeEventListener(\"change\", onChange)\n            })[\"useIsMobile.useEffect\"];\n        }\n    }[\"useIsMobile.useEffect\"], []);\n    return !!isMobile;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlLW1vYmlsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThCO0FBRTlCLE1BQU1DLG9CQUFvQjtBQUVuQixTQUFTQztJQUNkLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHSiwyQ0FBYyxDQUFzQk07SUFFcEVOLDRDQUFlO2lDQUFDO1lBQ2QsTUFBTVEsTUFBTUMsT0FBT0MsVUFBVSxDQUFDLENBQUMsWUFBWSxFQUFFVCxvQkFBb0IsRUFBRSxHQUFHLENBQUM7WUFDdkUsTUFBTVU7a0RBQVc7b0JBQ2ZQLFlBQVlLLE9BQU9HLFVBQVUsR0FBR1g7Z0JBQ2xDOztZQUNBTyxJQUFJSyxnQkFBZ0IsQ0FBQyxVQUFVRjtZQUMvQlAsWUFBWUssT0FBT0csVUFBVSxHQUFHWDtZQUNoQzt5Q0FBTyxJQUFNTyxJQUFJTSxtQkFBbUIsQ0FBQyxVQUFVSDs7UUFDakQ7Z0NBQUcsRUFBRTtJQUVMLE9BQU8sQ0FBQyxDQUFDUjtBQUNYIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxGTFxcVmVsb2NpdHlcXHN0dWR5YnVkZHktZnJvbnRlbmRcXHNyY1xcaG9va3NcXHVzZS1tb2JpbGUudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmNvbnN0IE1PQklMRV9CUkVBS1BPSU5UID0gNzY4XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VJc01vYmlsZSgpIHtcbiAgY29uc3QgW2lzTW9iaWxlLCBzZXRJc01vYmlsZV0gPSBSZWFjdC51c2VTdGF0ZTxib29sZWFuIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpXG5cbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBtcWwgPSB3aW5kb3cubWF0Y2hNZWRpYShgKG1heC13aWR0aDogJHtNT0JJTEVfQlJFQUtQT0lOVCAtIDF9cHgpYClcbiAgICBjb25zdCBvbkNoYW5nZSA9ICgpID0+IHtcbiAgICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDwgTU9CSUxFX0JSRUFLUE9JTlQpXG4gICAgfVxuICAgIG1xbC5hZGRFdmVudExpc3RlbmVyKFwiY2hhbmdlXCIsIG9uQ2hhbmdlKVxuICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDwgTU9CSUxFX0JSRUFLUE9JTlQpXG4gICAgcmV0dXJuICgpID0+IG1xbC5yZW1vdmVFdmVudExpc3RlbmVyKFwiY2hhbmdlXCIsIG9uQ2hhbmdlKVxuICB9LCBbXSlcblxuICByZXR1cm4gISFpc01vYmlsZVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTU9CSUxFX0JSRUFLUE9JTlQiLCJ1c2VJc01vYmlsZSIsImlzTW9iaWxlIiwic2V0SXNNb2JpbGUiLCJ1c2VTdGF0ZSIsInVuZGVmaW5lZCIsInVzZUVmZmVjdCIsIm1xbCIsIndpbmRvdyIsIm1hdGNoTWVkaWEiLCJvbkNoYW5nZSIsImlubmVyV2lkdGgiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-mobile.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirstLetter: () => (/* binding */ capitalizeFirstLetter),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   getFirstWord: () => (/* binding */ getFirstWord),\n/* harmony export */   getNonRepeatingValues: () => (/* binding */ getNonRepeatingValues),\n/* harmony export */   subjectOptions: () => (/* binding */ subjectOptions)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction getFirstWord(name) {\n    if (typeof name !== \"string\") return \"\";\n    const words = name.trim().split(\" \");\n    return words.length > 1 ? words[0] : name;\n}\nfunction capitalizeFirstLetter(name) {\n    if (typeof name !== \"string\") return \"\";\n    const letter = name.charAt(0).toUpperCase() + name.slice(1);\n    return letter;\n}\nconst subjectOptions = [\n    \"English\",\n    \"Mathematics\",\n    \"Geometry\",\n    \"Algebra\",\n    \"Numerical\",\n    \"Science\",\n    \"Chemistry\",\n    \"Biology\",\n    \"Physics\",\n    \"Social Science\",\n    \"Geography\",\n    \"Economics\",\n    \"Political Science\",\n    \"History\",\n    \"Computer Science\",\n    \"Electronics\",\n    \"Electricals\",\n    \"Statistics\"\n];\nfunction getNonRepeatingValues(array1, array2) {\n    const uniqueValues = new Set();\n    array1.forEach((value)=>{\n        if (!array2.includes(value)) {\n            uniqueValues.add(value);\n        }\n    });\n    array2.forEach((value)=>{\n        if (!array1.includes(value)) {\n            uniqueValues.add(value);\n        }\n    });\n    return Array.from(uniqueValues);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNEM7QUFDSjtBQUVqQyxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEI7QUFZTyxTQUFTQyxhQUFhQyxJQUFZO0lBQ3ZDLElBQUksT0FBT0EsU0FBUyxVQUFVLE9BQU87SUFFckMsTUFBTUMsUUFBUUQsS0FBS0UsSUFBSSxHQUFHQyxLQUFLLENBQUM7SUFDaEMsT0FBT0YsTUFBTUcsTUFBTSxHQUFHLElBQUlILEtBQUssQ0FBQyxFQUFFLEdBQUdEO0FBQ3ZDO0FBRU8sU0FBU0ssc0JBQXNCTCxJQUFZO0lBQ2hELElBQUksT0FBT0EsU0FBUyxVQUFVLE9BQU87SUFDckMsTUFBTU0sU0FBU04sS0FBS08sTUFBTSxDQUFDLEdBQUdDLFdBQVcsS0FBS1IsS0FBS1MsS0FBSyxDQUFDO0lBQ3pELE9BQU9IO0FBQ1Q7QUFFTyxNQUFNSSxpQkFBaUI7SUFDNUI7SUFDQTtJQUNGO0lBQ0U7SUFDQTtJQUNBO0lBQ0Y7SUFDRTtJQUNBO0lBQ0E7SUFDRjtJQUNFO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0QsQ0FBQztBQUVLLFNBQVNDLHNCQUFzQkMsTUFBZ0IsRUFBRUMsTUFBZ0I7SUFDdEUsTUFBTUMsZUFBZSxJQUFJQztJQUV6QkgsT0FBT0ksT0FBTyxDQUFDLENBQUNDO1FBQ2QsSUFBSSxDQUFDSixPQUFPSyxRQUFRLENBQUNELFFBQVE7WUFDM0JILGFBQWFLLEdBQUcsQ0FBQ0Y7UUFDbkI7SUFDRjtJQUVBSixPQUFPRyxPQUFPLENBQUMsQ0FBQ0M7UUFDZCxJQUFJLENBQUNMLE9BQU9NLFFBQVEsQ0FBQ0QsUUFBUTtZQUMzQkgsYUFBYUssR0FBRyxDQUFDRjtRQUNuQjtJQUNGO0lBRUEsT0FBT0csTUFBTUMsSUFBSSxDQUFDUDtBQUNwQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXFZlbG9jaXR5XFxzdHVkeWJ1ZGR5LWZyb250ZW5kXFxzcmNcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxyXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xyXG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBVc2VyRGF0YSB7XHJcbiAgZG9iOiBzdHJpbmc7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIHBob25lbm86IHN0cmluZztcclxuICBzY2hvb2xOYW1lOiBzdHJpbmc7XHJcbiAgY2xhc3M6IHN0cmluZztcclxuICBzdWJqZWN0czogc3RyaW5nW107XHJcbiAgcHJvZmlsZUltYWdlID86IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGdldEZpcnN0V29yZChuYW1lOiBzdHJpbmcpIHtcclxuICBpZiAodHlwZW9mIG5hbWUgIT09IFwic3RyaW5nXCIpIHJldHVybiBcIlwiO1xyXG5cclxuICBjb25zdCB3b3JkcyA9IG5hbWUudHJpbSgpLnNwbGl0KFwiIFwiKTtcclxuICByZXR1cm4gd29yZHMubGVuZ3RoID4gMSA/IHdvcmRzWzBdIDogbmFtZTtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGNhcGl0YWxpemVGaXJzdExldHRlcihuYW1lOiBzdHJpbmcpIHtcclxuICBpZiAodHlwZW9mIG5hbWUgIT09IFwic3RyaW5nXCIpIHJldHVybiBcIlwiO1xyXG4gIGNvbnN0IGxldHRlciA9IG5hbWUuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBuYW1lLnNsaWNlKDEpO1xyXG4gIHJldHVybiBsZXR0ZXJcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IHN1YmplY3RPcHRpb25zID0gW1xyXG4gIFwiRW5nbGlzaFwiLFxyXG4gIFwiTWF0aGVtYXRpY3NcIixcclxuXCJHZW9tZXRyeVwiLFxyXG4gIFwiQWxnZWJyYVwiLFxyXG4gIFwiTnVtZXJpY2FsXCIsXHJcbiAgXCJTY2llbmNlXCIsXHJcblwiQ2hlbWlzdHJ5XCIsXHJcbiAgXCJCaW9sb2d5XCIsXHJcbiAgXCJQaHlzaWNzXCIsXHJcbiAgXCJTb2NpYWwgU2NpZW5jZVwiLCBcclxuXCJHZW9ncmFwaHlcIixcclxuICBcIkVjb25vbWljc1wiLFxyXG4gIFwiUG9saXRpY2FsIFNjaWVuY2VcIixcclxuICBcIkhpc3RvcnlcIixcclxuICBcIkNvbXB1dGVyIFNjaWVuY2VcIixcclxuICBcIkVsZWN0cm9uaWNzXCIsXHJcbiAgXCJFbGVjdHJpY2Fsc1wiLFxyXG4gIFwiU3RhdGlzdGljc1wiXHJcbl07XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gZ2V0Tm9uUmVwZWF0aW5nVmFsdWVzKGFycmF5MTogc3RyaW5nW10sIGFycmF5Mjogc3RyaW5nW10pOiBzdHJpbmdbXSB7XHJcbiAgY29uc3QgdW5pcXVlVmFsdWVzID0gbmV3IFNldDxzdHJpbmc+KCk7XHJcblxyXG4gIGFycmF5MS5mb3JFYWNoKCh2YWx1ZSkgPT4ge1xyXG4gICAgaWYgKCFhcnJheTIuaW5jbHVkZXModmFsdWUpKSB7XHJcbiAgICAgIHVuaXF1ZVZhbHVlcy5hZGQodmFsdWUpO1xyXG4gICAgfVxyXG4gIH0pO1xyXG5cclxuICBhcnJheTIuZm9yRWFjaCgodmFsdWUpID0+IHtcclxuICAgIGlmICghYXJyYXkxLmluY2x1ZGVzKHZhbHVlKSkge1xyXG4gICAgICB1bmlxdWVWYWx1ZXMuYWRkKHZhbHVlKTtcclxuICAgIH1cclxuICB9KTtcclxuXHJcbiAgcmV0dXJuIEFycmF5LmZyb20odW5pcXVlVmFsdWVzKTtcclxufVxyXG5cclxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiLCJnZXRGaXJzdFdvcmQiLCJuYW1lIiwid29yZHMiLCJ0cmltIiwic3BsaXQiLCJsZW5ndGgiLCJjYXBpdGFsaXplRmlyc3RMZXR0ZXIiLCJsZXR0ZXIiLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsInNsaWNlIiwic3ViamVjdE9wdGlvbnMiLCJnZXROb25SZXBlYXRpbmdWYWx1ZXMiLCJhcnJheTEiLCJhcnJheTIiLCJ1bmlxdWVWYWx1ZXMiLCJTZXQiLCJmb3JFYWNoIiwidmFsdWUiLCJpbmNsdWRlcyIsImFkZCIsIkFycmF5IiwiZnJvbSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f2b318c519c1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxGTFxcVmVsb2NpdHlcXHN0dWR5YnVkZHktZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImYyYjMxOGM1MTljMVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/subject-card */ \"(rsc)/./src/components/dashboard/subject-card.tsx\");\n/* harmony import */ var _components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/recommendation-card */ \"(rsc)/./src/components/dashboard/recommendation-card.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/dashboard-header */ \"(rsc)/./src/components/dashboard/dashboard-header.tsx\");\n/* harmony import */ var _components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/app-sidebar */ \"(rsc)/./src/components/dashboard/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(rsc)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n\n\n\n\n\n\n\nconst subjects = [\n    {\n        subject: \"Maths\",\n        color: \"bg-blue-500\",\n        image: \"/placeholder.svg?height=64&width=64\"\n    },\n    {\n        subject: \"Physics\",\n        color: \"bg-orange-500\",\n        image: \"/placeholder.svg?height=64&width=64\"\n    },\n    {\n        subject: \"Biology\",\n        color: \"bg-red-500\",\n        image: \"/placeholder.svg?height=64&width=64\"\n    },\n    {\n        subject: \"Chemistry\",\n        color: \"bg-green-500\",\n        image: \"/placeholder.svg?height=64&width=64\"\n    }\n];\nconst recommendations = [\n    {\n        subject: \"Algebra\",\n        score: \"6/10\",\n        color: \"bg-blue-50\",\n        buttonColor: \"bg-blue-500\"\n    },\n    {\n        subject: \"Algebra\",\n        score: \"6/10\",\n        color: \"bg-blue-50\",\n        buttonColor: \"bg-blue-500\"\n    },\n    {\n        subject: \"Velocity\",\n        score: \"6/10\",\n        color: \"bg-orange-50\",\n        buttonColor: \"bg-orange-500\"\n    },\n    {\n        subject: \"Evolution\",\n        score: \"6/10\",\n        color: \"bg-red-50\",\n        buttonColor: \"bg-red-500\"\n    },\n    {\n        subject: \"Evolution\",\n        score: \"6/10\",\n        color: \"bg-red-50\",\n        buttonColor: \"bg-red-500\"\n    },\n    {\n        subject: \"Organic Theory\",\n        score: \"6/10\",\n        color: \"bg-green-50\",\n        buttonColor: \"bg-green-500\"\n    },\n    {\n        subject: \"Algebra\",\n        score: \"6/10\",\n        color: \"bg-blue-50\",\n        buttonColor: \"bg-blue-500\"\n    },\n    {\n        subject: \"Velocity\",\n        score: \"6/10\",\n        color: \"bg-orange-50\",\n        buttonColor: \"bg-orange-500\"\n    }\n];\nfunction Dashboard() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_4__.AppSidebar, {\n                currentPage: \"dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_3__.DashboardHeader, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 md:p-6 space-y-6 bg-gray-50 min-h-screen\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-medium text-gray-800 mb-4\",\n                                    children: \"Welcome back, Arjun \\uD83D\\uDC4B\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                        children: \"Continue Learning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: subjects.map((subject, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_1__.SubjectCard, {\n                                                subject: subject.subject,\n                                                color: subject.color,\n                                                image: subject.image\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Smart Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_2__.RecommendationCard, {\n                                                subject: rec.subject,\n                                                score: rec.score,\n                                                color: rec.color,\n                                                buttonColor: rec.buttonColor\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Manrope_arguments_subsets_latin_variableName_manrope___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Manrope\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"manrope\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Manrope\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"manrope\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Manrope_arguments_subsets_latin_variableName_manrope___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Manrope_arguments_subsets_latin_variableName_manrope___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: 'StudyBuddy - Your Ultimate Study Companion',\n    description: 'Personalized tools to boost your preparation for IIT & NEET'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Manrope_arguments_subsets_latin_variableName_manrope___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFHTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULG1LQUFpQjtzQkFBR0s7Ozs7Ozs7Ozs7O0FBRzNDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxGTFxcVmVsb2NpdHlcXHN0dWR5YnVkZHktZnJvbnRlbmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE1hbnJvcGUgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5jb25zdCBtYW5yb3BlID0gTWFucm9wZSh7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnU3R1ZHlCdWRkeSAtIFlvdXIgVWx0aW1hdGUgU3R1ZHkgQ29tcGFuaW9uJyxcbiAgZGVzY3JpcHRpb246ICdQZXJzb25hbGl6ZWQgdG9vbHMgdG8gYm9vc3QgeW91ciBwcmVwYXJhdGlvbiBmb3IgSUlUICYgTkVFVCcsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXttYW5yb3BlLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cblxuIl0sIm5hbWVzIjpbIm1hbnJvcGUiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/dashboard/app-sidebar.tsx":
/*!**************************************************!*\
  !*** ./src/components/dashboard/app-sidebar.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Lightbulb,LogOut,Trophy!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Lightbulb,LogOut,Trophy!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Lightbulb,LogOut,Trophy!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Lightbulb,LogOut,Trophy!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(rsc)/./src/components/ui/sidebar.tsx\");\n\n\n\nfunction AppSidebar({ currentPage = \"dashboard\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.Sidebar, {\n        className: \"border-r border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarHeader, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xl font-bold text-gray-800\",\n                            children: \"study\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xl font-bold text-blue-500\",\n                            children: \"buddy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroup, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroupLabel, {\n                            className: \"text-gray-500 text-sm font-medium mb-4\",\n                            children: \"Main Menu\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroupContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenuItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenuButton, {\n                                            asChild: true,\n                                            isActive: currentPage === \"dashboard\",\n                                            className: currentPage === \"dashboard\" ? \"bg-blue-500 text-white hover:bg-blue-600 rounded-full\" : \"text-gray-600 hover:text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/\",\n                                                className: \"flex items-center gap-3 px-4 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                        lineNumber: 45,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"My Dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                        lineNumber: 46,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenuItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenuButton, {\n                                            asChild: true,\n                                            isActive: currentPage === \"dashboard/leaderboard\",\n                                            className: currentPage === \"dashboard/leaderboard\" ? \"bg-blue-500 text-white hover:bg-blue-600 rounded-full\" : \"text-gray-600 hover:text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/leaderboard\",\n                                                className: \"flex items-center gap-3 px-4 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Leaderboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                        lineNumber: 62,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarFooter, {\n                className: \"p-4 space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-orange-50 border border-orange-200 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-orange-700\",\n                                        children: \"More features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-orange-600\",\n                                children: \"Coming soon!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"flex items-center gap-2 w-full p-3 text-red-500 bg-red-50 rounded-lg hover:bg-red-100 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/dashboard/app-sidebar.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/dashboard/dashboard-header.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/dashboard-header.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardHeader: () => (/* binding */ DashboardHeader)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const DashboardHeader = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call DashboardHeader() from the server but DashboardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\dashboard\\dashboard-header.tsx",
"DashboardHeader",
);

/***/ }),

/***/ "(rsc)/./src/components/dashboard/recommendation-card.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/recommendation-card.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecommendationCard: () => (/* binding */ RecommendationCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction RecommendationCard({ subject, score, color, buttonColor }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${color} rounded-lg p-4 space-y-3`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium\",\n                        children: [\n                            \"You Scored \",\n                            score\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm opacity-90\",\n                        children: [\n                            \"in \",\n                            subject\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: `w-full ${buttonColor} text-white py-2 px-4 rounded-md text-sm font-medium hover:opacity-90 transition-opacity`,\n                children: \"Revise Again\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvcmVjb21tZW5kYXRpb24tY2FyZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQU9PLFNBQVNBLG1CQUFtQixFQUFFQyxPQUFPLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFQyxXQUFXLEVBQTJCO0lBQ2hHLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFXLEdBQUdILE1BQU0seUJBQXlCLENBQUM7OzBCQUNqRCw4REFBQ0U7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7NEJBQWM7NEJBQVlKOzs7Ozs7O2tDQUN6Qyw4REFBQ0c7d0JBQUlDLFdBQVU7OzRCQUFxQjs0QkFBSUw7Ozs7Ozs7Ozs7Ozs7MEJBRTFDLDhEQUFDTTtnQkFDQ0QsV0FBVyxDQUFDLE9BQU8sRUFBRUYsWUFBWSx3RkFBd0YsQ0FBQzswQkFDM0g7Ozs7Ozs7Ozs7OztBQUtQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxGTFxcVmVsb2NpdHlcXHN0dWR5YnVkZHktZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcZGFzaGJvYXJkXFxyZWNvbW1lbmRhdGlvbi1jYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbnRlcmZhY2UgUmVjb21tZW5kYXRpb25DYXJkUHJvcHMge1xyXG4gIHN1YmplY3Q6IHN0cmluZ1xyXG4gIHNjb3JlOiBzdHJpbmdcclxuICBjb2xvcjogc3RyaW5nXHJcbiAgYnV0dG9uQ29sb3I6IHN0cmluZ1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUmVjb21tZW5kYXRpb25DYXJkKHsgc3ViamVjdCwgc2NvcmUsIGNvbG9yLCBidXR0b25Db2xvciB9OiBSZWNvbW1lbmRhdGlvbkNhcmRQcm9wcykge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT17YCR7Y29sb3J9IHJvdW5kZWQtbGcgcC00IHNwYWNlLXktM2B9PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc21cIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+WW91IFNjb3JlZCB7c2NvcmV9PC9kaXY+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIG9wYWNpdHktOTBcIj5pbiB7c3ViamVjdH08L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxidXR0b25cclxuICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgJHtidXR0b25Db2xvcn0gdGV4dC13aGl0ZSBweS0yIHB4LTQgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIGhvdmVyOm9wYWNpdHktOTAgdHJhbnNpdGlvbi1vcGFjaXR5YH1cclxuICAgICAgPlxyXG4gICAgICAgIFJldmlzZSBBZ2FpblxyXG4gICAgICA8L2J1dHRvbj5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsiUmVjb21tZW5kYXRpb25DYXJkIiwic3ViamVjdCIsInNjb3JlIiwiY29sb3IiLCJidXR0b25Db2xvciIsImRpdiIsImNsYXNzTmFtZSIsImJ1dHRvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/dashboard/recommendation-card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/dashboard/subject-card.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/subject-card.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SubjectCard: () => (/* binding */ SubjectCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction SubjectCard({ subject, color, textColor = \"text-white\", image }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${color} rounded-xl p-6 relative overflow-hidden min-h-[120px] flex items-center justify-between`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-sm ${textColor} opacity-90 mb-1`,\n                        children: \"Learn\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\subject-card.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-lg font-semibold ${textColor}`,\n                        children: subject\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\subject-card.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\subject-card.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-4 top-1/2 transform -translate-y-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: image || \"/placeholder.svg\",\n                    alt: `${subject} character`,\n                    className: \"w-16 h-16 object-cover rounded-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\subject-card.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\subject-card.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\subject-card.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/dashboard/subject-card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/sidebar.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/sidebar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Sidebar: () => (/* binding */ Sidebar),
/* harmony export */   SidebarContent: () => (/* binding */ SidebarContent),
/* harmony export */   SidebarFooter: () => (/* binding */ SidebarFooter),
/* harmony export */   SidebarGroup: () => (/* binding */ SidebarGroup),
/* harmony export */   SidebarGroupAction: () => (/* binding */ SidebarGroupAction),
/* harmony export */   SidebarGroupContent: () => (/* binding */ SidebarGroupContent),
/* harmony export */   SidebarGroupLabel: () => (/* binding */ SidebarGroupLabel),
/* harmony export */   SidebarHeader: () => (/* binding */ SidebarHeader),
/* harmony export */   SidebarInput: () => (/* binding */ SidebarInput),
/* harmony export */   SidebarInset: () => (/* binding */ SidebarInset),
/* harmony export */   SidebarMenu: () => (/* binding */ SidebarMenu),
/* harmony export */   SidebarMenuAction: () => (/* binding */ SidebarMenuAction),
/* harmony export */   SidebarMenuBadge: () => (/* binding */ SidebarMenuBadge),
/* harmony export */   SidebarMenuButton: () => (/* binding */ SidebarMenuButton),
/* harmony export */   SidebarMenuItem: () => (/* binding */ SidebarMenuItem),
/* harmony export */   SidebarMenuSkeleton: () => (/* binding */ SidebarMenuSkeleton),
/* harmony export */   SidebarMenuSub: () => (/* binding */ SidebarMenuSub),
/* harmony export */   SidebarMenuSubButton: () => (/* binding */ SidebarMenuSubButton),
/* harmony export */   SidebarMenuSubItem: () => (/* binding */ SidebarMenuSubItem),
/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),
/* harmony export */   SidebarRail: () => (/* binding */ SidebarRail),
/* harmony export */   SidebarSeparator: () => (/* binding */ SidebarSeparator),
/* harmony export */   SidebarTrigger: () => (/* binding */ SidebarTrigger),
/* harmony export */   useSidebar: () => (/* binding */ useSidebar)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Sidebar = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"Sidebar",
);const SidebarContent = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarContent",
);const SidebarFooter = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarFooter",
);const SidebarGroup = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarGroup",
);const SidebarGroupAction = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarGroupAction",
);const SidebarGroupContent = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarGroupContent",
);const SidebarGroupLabel = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarGroupLabel",
);const SidebarHeader = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarHeader",
);const SidebarInput = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarInput",
);const SidebarInset = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarInset",
);const SidebarMenu = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarMenu",
);const SidebarMenuAction = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarMenuAction",
);const SidebarMenuBadge = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarMenuBadge",
);const SidebarMenuButton = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarMenuButton",
);const SidebarMenuItem = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarMenuItem",
);const SidebarMenuSkeleton = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarMenuSkeleton",
);const SidebarMenuSub = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarMenuSub",
);const SidebarMenuSubButton = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarMenuSubButton",
);const SidebarMenuSubItem = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarMenuSubItem",
);const SidebarProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarProvider",
);const SidebarRail = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarRail",
);const SidebarSeparator = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarSeparator",
);const SidebarTrigger = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"SidebarTrigger",
);const useSidebar = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\FL\\Velocity\\studybuddy-frontend\\src\\components\\ui\\sidebar.tsx",
"useSidebar",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWRhcnNcXERlc2t0b3BcXEZMXFxWZWxvY2l0eVxcc3R1ZHlidWRkeS1mcm9udGVuZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/aria-hidden","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/react-style-singleton","vendor-chunks/get-nonce","vendor-chunks/@floating-ui"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5CVelocity%5Cstudybuddy-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5CVelocity%5Cstudybuddy-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();