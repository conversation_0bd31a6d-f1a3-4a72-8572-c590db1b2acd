{"name": "shadcn-ui", "version": "0.9.4", "description": "Add components to your apps.", "publishConfig": {"access": "public"}, "license": "MIT", "author": {"name": "shadcn", "url": "https://twitter.com/shadcn"}, "repository": {"type": "git", "url": "https://github.com/shadcn/ui.git", "directory": "packages/cli"}, "files": ["dist"], "keywords": ["components", "ui", "tailwind", "radix-ui", "shadcn"], "type": "module", "exports": "./dist/index.js", "bin": "./dist/index.js", "dependencies": {"@antfu/ni": "^0.21.4", "@babel/core": "^7.22.1", "@babel/parser": "^7.22.6", "@babel/plugin-transform-typescript": "^7.22.5", "chalk": "5.2.0", "commander": "^10.0.0", "cosmiconfig": "^8.1.3", "diff": "^5.1.0", "execa": "^7.0.0", "fast-glob": "^3.3.2", "fs-extra": "^11.1.0", "https-proxy-agent": "^6.2.0", "lodash": "^4.17.21", "node-fetch": "^3.3.0", "ora": "^6.1.2", "prompts": "^2.4.2", "recast": "^0.23.2", "ts-morph": "^18.0.0", "tsconfig-paths": "^4.2.0", "zod": "^3.20.2"}, "devDependencies": {"@types/babel__core": "^7.20.1", "@types/diff": "^5.0.3", "@types/fs-extra": "^11.0.1", "@types/lodash": "^4.17.7", "@types/prompts": "^2.4.2", "rimraf": "^4.1.3", "tsup": "^6.6.3", "type-fest": "^3.8.0", "typescript": "^4.9.3"}, "scripts": {"dev": "tsup --watch", "build": "tsup", "typecheck": "tsc --noEmit", "clean": "rimraf dist && rimraf components", "start:dev": "cross-env COMPONENTS_REGISTRY_URL=http://localhost:3003 node dist/index.js", "start": "node dist/index.js", "format:write": "prettier --write \"**/*.{ts,tsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,mdx}\" --cache", "release": "changeset version", "pub:beta": "pnpm build && pnpm publish --no-git-checks --access public --tag beta", "pub:next": "pnpm build && pnpm publish --no-git-checks --access public --tag next", "pub:release": "pnpm build && pnpm publish --access public", "test": "vitest run", "test:dev": "REGISTRY_URL=http://localhost:3333 vitest run"}}