!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r(require("react"),require("katex")):"function"==typeof define&&define.amd?define(["react","katex"],r):(e=e||self).TeX=r(e.react,e.katex)}(this,function(e,r){var t="default"in e?e.default:e;function n(){return(n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}return r=r&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r,e.memo(function(o){var a=o.children,i=o.math,l=o.block,f=o.errorColor,c=o.renderError,s=o.settings,u=o.as,d=function(e,r){if(null==e)return{};var t,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r.indexOf(t=a[n])>=0||(o[t]=e[t]);return o}(o,["children","math","block","errorColor","renderError","settings","as"]),m=u||(l?"div":"span"),p=null!=a?a:i,h=e.useState({innerHtml:""}),y=h[0],E=h[1];return e.useEffect(function(){try{var e=r.renderToString(p,n({displayMode:!!l,errorColor:f,throwOnError:!!c},s));E({innerHtml:e})}catch(e){if(!(e instanceof r.ParseError||e instanceof TypeError))throw e;E(c?{errorElement:c(e)}:{innerHtml:e.message})}},[l,p,f,c,s]),"errorElement"in y?y.errorElement:t.createElement(m,Object.assign({},d,{dangerouslySetInnerHTML:{__html:y.innerHtml}}))})});
//# sourceMappingURL=react-katex.umd.js.map
