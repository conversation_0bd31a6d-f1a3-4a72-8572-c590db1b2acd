import r,{memo as e,useState as n,useEffect as t}from"react";import o from"katex";var a=e(({children:e,math:a,block:i,errorColor:l,renderError:m,settings:s,as:c,...d})=>{const E=c||(i?"div":"span"),f=e??a,[h,p]=n({innerHtml:""});return t(()=>{try{const r=o.renderToString(f,{displayMode:!!i,errorColor:l,throwOnError:!!m,...s});p({innerHtml:r})}catch(r){if(!(r instanceof o.ParseError||r instanceof TypeError))throw r;p(m?{errorElement:m(r)}:{innerHtml:r.message})}},[i,f,l,m,s]),"errorElement"in h?h.errorElement:r.createElement(E,Object.assign({},d,{dangerouslySetInnerHTML:{__html:h.innerHtml}}))});export default a;
//# sourceMappingURL=react-katex.modern.js.map
