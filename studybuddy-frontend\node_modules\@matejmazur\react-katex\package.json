{"name": "@matejmazur/react-katex", "version": "3.1.3", "description": "Display math in TeX with KaTeX and ReactJS", "keywords": ["react", "tex", "latex", "math", "katex"], "homepage": "https://github.com/<PERSON><PERSON>/react-katex", "source": "src/index.tsx", "main": "dist/react-katex.js", "module": "dist/react-katex.m.js", "esmodule": "dist/react-katex.modern.js", "unpkg": "dist/react-katex.umd.js", "types": "dist/index.d.ts", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "**************:<PERSON><PERSON><PERSON><PERSON><PERSON>/react-katex.git"}, "bugs": {"url": "https://github.com/<PERSON><PERSON><PERSON>/react-katex/issues"}, "license": "MIT", "scripts": {"start": "cd demo && yarn start", "build": "rimraf dist && microbundle --jsx React.createElement --name TeX", "dev": "microbundle watch", "format": "prettier --write .", "lint": "eslint .", "types": "tsc -p tsconfig.json --noEmit", "test": "jest", "prepublishOnly": "npm run build"}, "engines": {"node": ">=12", "yarn": ">=1.1"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"{index,index.spec,demo/index}.js": ["prettier --write", "eslint", "git add"]}, "devDependencies": {"@matejbransky/eslint-config": "1.0.0-alpha.2", "@matejbransky/jest-preset": "1.0.0-alpha.2", "@matejbransky/prettier-config": "1.0.0-alpha.2", "@matejbransky/typescript-config": "1.0.0-alpha.1", "@testing-library/jest-dom": "^5.11.2", "@testing-library/react": "^10.4.8", "@types/katex": "^0.11.0", "@types/react": "^16.9.44", "@types/react-dom": "^16.9.8", "eslint": "6.x", "husky": "^4.2.5", "jest": "^26.2.2", "katex": "^0.12.0", "lint-staged": "^10.2.11", "microbundle": "^0.12.3", "prettier": "^2.0.5", "react": "^16.13.1", "react-dom": "^16.13.1", "react-error-boundary": "^2.3.1", "rimraf": "^3.0.2", "snapshot-diff": "^0.8.1", "typescript": "^3.9.7"}, "peerDependencies": {"katex": ">=0.9", "react": ">=16"}, "jest": {"preset": "@matej<PERSON><PERSON>/jest-preset", "testEnvironment": "jsdom"}, "eslintConfig": {"extends": "@matej<PERSON><PERSON>/eslint-config"}, "prettier": "@matej<PERSON><PERSON>/prettier-config"}