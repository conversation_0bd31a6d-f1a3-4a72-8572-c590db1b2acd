function r(r){return r&&"object"==typeof r&&"default"in r?r.default:r}var e=require("react"),n=r(e),t=r(require("katex"));function o(){return(o=Object.assign||function(r){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(r[t]=n[t])}return r}).apply(this,arguments)}var a=e.memo(function(r){var a=r.children,i=r.math,l=r.block,s=r.errorColor,c=r.renderError,u=r.settings,f=r.as,m=function(r,e){if(null==r)return{};var n,t,o={},a=Object.keys(r);for(t=0;t<a.length;t++)e.indexOf(n=a[t])>=0||(o[n]=r[n]);return o}(r,["children","math","block","errorColor","renderError","settings","as"]),d=f||(l?"div":"span"),h=null!=a?a:i,p=e.useState({innerHtml:""}),E=p[0],g=p[1];return e.useEffect(function(){try{var r=t.renderToString(h,o({displayMode:!!l,errorColor:s,throwOnError:!!c},u));g({innerHtml:r})}catch(r){if(!(r instanceof t.ParseError||r instanceof TypeError))throw r;g(c?{errorElement:c(r)}:{innerHtml:r.message})}},[l,h,s,c,u]),"errorElement"in E?E.errorElement:n.createElement(d,Object.assign({},m,{dangerouslySetInnerHTML:{__html:E.innerHtml}}))});module.exports=a;
//# sourceMappingURL=react-katex.js.map
