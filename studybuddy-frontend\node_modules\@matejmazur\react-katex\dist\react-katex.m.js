import r,{memo as e,useState as n,useEffect as t}from"react";import o from"katex";function a(){return(a=Object.assign||function(r){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(r[t]=n[t])}return r}).apply(this,arguments)}var i=e(function(e){var i=e.children,l=e.math,s=e.block,c=e.errorColor,f=e.renderError,m=e.settings,u=e.as,h=function(r,e){if(null==r)return{};var n,t,o={},a=Object.keys(r);for(t=0;t<a.length;t++)e.indexOf(n=a[t])>=0||(o[n]=r[n]);return o}(e,["children","math","block","errorColor","renderError","settings","as"]),d=u||(s?"div":"span"),p=null!=i?i:l,g=n({innerHtml:""}),E=g[0],v=g[1];return t(function(){try{var r=o.renderToString(p,a({displayMode:!!s,errorColor:c,throwOnError:!!f},m));v({innerHtml:r})}catch(r){if(!(r instanceof o.ParseError||r instanceof TypeError))throw r;v(f?{errorElement:f(r)}:{innerHtml:r.message})}},[s,p,c,f,m]),"errorElement"in E?E.errorElement:r.createElement(d,Object.assign({},h,{dangerouslySetInnerHTML:{__html:E.innerHtml}}))});export default i;
//# sourceMappingURL=react-katex.m.js.map
